<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Systematic Jewish Cipher Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .mismatch { background-color: #ffe4e1; font-weight: bold; }
        .match { background-color: #e4ffe4; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Systematic Jewish Cipher Analysis</h1>
    
    <div class="test-section">
        <h3>Test Results</h3>
        <table id="resultsTable">
            <thead>
                <tr>
                    <th>Input</th>
                    <th>Type</th>
                    <th>Code 1 Hebrew Ordinal</th>
                    <th>Code 2 Jewish Ordinal</th>
                    <th>Match?</th>
                    <th>Code 1 Hebrew Gematria</th>
                    <th>Code 2 Jewish</th>
                    <th>Match?</th>
                </tr>
            </thead>
            <tbody id="resultsBody">
            </tbody>
        </table>
    </div>

    <!-- Load Code 1 -->
    <script src="Code 1/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        // Store Code 1 functions
        const code1_allCiphers = [...allCiphers];
        allCiphers.length = 0;
    </script>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        // Initialize Code 2
        Set_Categories();
        Build_Ciphers();
        const code2_allCiphers = [...allCiphers];

        function getCipherValue(cipherList, cipherName, text) {
            for (let i = 0; i < cipherList.length; i++) {
                if (cipherList[i].Nickname === cipherName) {
                    return cipherList[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function addTestResult(input, type, code1Ordinal, code2Ordinal, code1Gematria, code2Gematria) {
            const tbody = document.getElementById('resultsBody');
            const row = tbody.insertRow();
            
            const ordinalMatch = code1Ordinal === code2Ordinal;
            const gematriaMatch = code1Gematria === code2Gematria;
            
            row.innerHTML = `
                <td>${input}</td>
                <td>${type}</td>
                <td>${code1Ordinal}</td>
                <td>${code2Ordinal}</td>
                <td class="${ordinalMatch ? 'match' : 'mismatch'}">${ordinalMatch ? '✅' : '❌'}</td>
                <td>${code1Gematria}</td>
                <td>${code2Gematria}</td>
                <td class="${gematriaMatch ? 'match' : 'mismatch'}">${gematriaMatch ? '✅' : '❌'}</td>
            `;
        }

        function runSystematicTest() {
            // Test various types of inputs
            const testCases = [
                // Single letters
                { inputs: ['a', 'b', 'c', 'x', 'y', 'z'], type: 'Single Letters' },
                
                // Short words
                { inputs: ['ab', 'abc', 'xyz', 'cat', 'dog'], type: 'Short Words' },
                
                // Medium words
                { inputs: ['hello', 'world', 'test', 'gematria', 'cipher'], type: 'Medium Words' },
                
                // Long words
                { inputs: ['calculator', 'mathematics', 'programming', 'understanding'], type: 'Long Words' },
                
                // Words with repeated letters
                { inputs: ['aaa', 'abba', 'hello', 'letter', 'coffee'], type: 'Repeated Letters' },
                
                // Mixed case (should be handled the same)
                { inputs: ['Hello', 'WORLD', 'TeSt'], type: 'Mixed Case' },
                
                // Words with spaces
                { inputs: ['hello world', 'test case', 'a b c'], type: 'With Spaces' },
                
                // Words with numbers
                { inputs: ['test123', 'abc1', 'hello2world'], type: 'With Numbers' },
                
                // Special characters
                { inputs: ['hello!', 'test?', 'a-b'], type: 'With Special Chars' },
                
                // Edge cases
                { inputs: ['', ' ', '  '], type: 'Edge Cases' }
            ];

            for (const testGroup of testCases) {
                for (const input of testGroup.inputs) {
                    const code1Ordinal = getCipherValue(code1_allCiphers, 'Hebrew Ordinal', input);
                    const code2Ordinal = getCipherValue(code2_allCiphers, 'Jewish Ordinal', input);
                    const code1Gematria = getCipherValue(code1_allCiphers, 'Hebrew Gematria', input);
                    const code2Gematria = getCipherValue(code2_allCiphers, 'Jewish', input);
                    
                    addTestResult(input || '(empty)', testGroup.type, code1Ordinal, code2Ordinal, code1Gematria, code2Gematria);
                }
            }
        }

        // Run the test
        runSystematicTest();
    </script>
</body>
</html>
