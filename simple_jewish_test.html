<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Jewish Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Simple Jewish Cipher Test</h1>
    
    <div id="results"></div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function testJewishCiphers() {
            const resultsDiv = document.getElementById('results');
            let html = '';
            
            // Test with simple word "CAT"
            const testWord = 'CAT';
            
            // Find Jewish ciphers
            for (let i = 0; i < allCiphers.length; i++) {
                const cipher = allCiphers[i];
                if (cipher.Nickname.includes('Jewish')) {
                    const result = cipher.Gematria(testWord, 1);
                    
                    html += `<div class="result">`;
                    html += `<strong>${cipher.Nickname}</strong>: "${testWord}" = ${result}`;
                    html += `<br>isJewish: ${cipher.isJewish}`;
                    html += `<br>Character codes: ${cipher.cArr ? cipher.cArr.slice(0, 5).join(',') + '...' : 'undefined'}`;
                    html += `</div>`;
                }
            }
            
            // Expected results
            html += `<div class="result" style="background-color: #f0f8ff;">`;
            html += `<strong>Expected for "CAT":</strong><br>`;
            html += `Jewish: 204 (C=3 + A=1 + T=200)<br>`;
            html += `Jewish Reduction: 6 (C=3 + A=1 + T=2)<br>`;
            html += `Jewish Ordinal: 24 (C=3 + A=1 + T=20)`;
            html += `</div>`;
            
            resultsDiv.innerHTML = html;
        }

        testJewishCiphers();
    </script>
</body>
</html>
