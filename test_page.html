<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gematria Calculator Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 300px;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        .cipher-result {
            margin: 5px 0;
            padding: 5px;
            background: white;
            border-radius: 3px;
            display: flex;
            justify-content: space-between;
        }
        .cipher-name {
            font-weight: bold;
        }
        .cipher-value {
            color: #007bff;
            font-weight: bold;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gematria Calculator Test Page</h1>
        <p>This page tests the Code 2 gematria calculator to ensure it works correctly with the new cipher system.</p>
        
        <div class="test-section">
            <h2>Manual Test</h2>
            <input type="text" id="testInput" class="test-input" placeholder="Enter text to test" value="hello world">
            <br>
            <button class="test-button" onclick="runSingleTest()">Test Single Input</button>
            <button class="test-button" onclick="runAllTests()">Run All Predefined Tests</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div class="test-section">
            <h2>Test Results</h2>
            <div id="results" class="results">
                <p>Click a test button to see results...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Console Log</h2>
            <div id="console" class="log">
                Console output will appear here...
            </div>
        </div>
    </div>

    <!-- Load Code 2 cipher system -->
    <script src="Code 2/ciphers.js"></script>
    <script src="test_comparison.js"></script>
    
    <script>
        // Override console.log to display in our custom console
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const consoleDiv = document.getElementById('console');
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            consoleDiv.textContent += args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            consoleDiv.textContent += 'ERROR: ' + args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        // Initialize the cipher system
        console.log('Initializing Code 2 cipher system...');
        try {
            Gem_Launch();
            console.log('✅ Code 2 cipher system initialized successfully');
            console.log(`Total ciphers loaded: ${allCiphers.length}`);
        } catch (error) {
            console.error('❌ Failed to initialize Code 2 cipher system:', error);
        }
        
        function runSingleTest() {
            const input = document.getElementById('testInput').value.trim();
            if (!input) {
                alert('Please enter some text to test');
                return;
            }
            
            console.log(`\n=== Testing: "${input}" ===`);
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<h3>Results for: "${input}"</h3>`;
            
            if (typeof allCiphers !== 'undefined' && allCiphers.length > 0) {
                for (let i = 0; i < allCiphers.length; i++) {
                    const cipher = allCiphers[i];
                    const cipherName = cipher.Nickname;
                    
                    try {
                        const value = cipher.Gematria(input, 1);
                        console.log(`${cipherName}: ${value}`);
                        
                        const resultDiv = document.createElement('div');
                        resultDiv.className = 'cipher-result';
                        resultDiv.innerHTML = `
                            <span class="cipher-name">${cipherName}</span>
                            <span class="cipher-value">${value}</span>
                        `;
                        resultsDiv.appendChild(resultDiv);
                    } catch (error) {
                        console.error(`Error calculating ${cipherName}:`, error);
                        
                        const resultDiv = document.createElement('div');
                        resultDiv.className = 'cipher-result error';
                        resultDiv.innerHTML = `
                            <span class="cipher-name">${cipherName}</span>
                            <span class="cipher-value">ERROR</span>
                        `;
                        resultsDiv.appendChild(resultDiv);
                    }
                }
            } else {
                console.error('Ciphers not loaded properly');
                resultsDiv.innerHTML = '<p class="error">Ciphers not loaded properly</p>';
            }
        }
        
        function runAllTests() {
            console.log('\n=== Running All Predefined Tests ===');
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>Running All Tests...</h3>';
            
            if (typeof GematriaTest !== 'undefined') {
                GematriaTest.runAllTests();
                resultsDiv.innerHTML = '<h3>All tests completed. Check console for detailed results.</h3>';
            } else {
                console.error('Test system not loaded properly');
                resultsDiv.innerHTML = '<p class="error">Test system not loaded properly</p>';
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '<p>Results cleared. Click a test button to see new results...</p>';
            document.getElementById('console').textContent = 'Console cleared...\n';
        }
    </script>
</body>
</html>
