<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fixed Ciphers</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .correct { background-color: #e4ffe4; font-weight: bold; }
        .incorrect { background-color: #ffe4e1; font-weight: bold; }
        .manual { background-color: #f0f8ff; padding: 10px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Test Fixed Ciphers</h1>
    
    <div class="manual">
        <h3>Expected Values for Each Cipher</h3>
        <table>
            <tr>
                <th>Cipher</th>
                <th>Letter Order</th>
                <th>Values</th>
            </tr>
            <tr>
                <td>Single Reduction</td>
                <td>a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z</td>
                <td>1,2,3,4,5,6,7,8,9,1,2,3,4,5,6,7,8,9,10,2,3,4,5,6,7,8</td>
            </tr>
            <tr>
                <td>Single Reduction KV</td>
                <td>a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z</td>
                <td>1,2,3,4,5,6,7,8,9,1,11,3,4,5,6,7,8,9,10,2,3,22,5,6,7,8</td>
            </tr>
            <tr>
                <td>Extended English</td>
                <td>a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z</td>
                <td>1,2,3,4,5,6,7,8,9,10,20,30,40,50,60,70,80,90,100,200,300,400,500,600,700,800</td>
            </tr>
            <tr>
                <td>Reverse Single Reduction</td>
                <td>z,y,x,w,v,u,t,s,r,q,p,o,n,m,l,k,j,i,h,g,f,e,d,c,b,a</td>
                <td>1,2,3,4,5,6,7,8,9,1,2,3,4,5,6,7,8,9,10,2,3,4,5,6,7,8</td>
            </tr>
            <tr>
                <td>Reverse Single Reduction EP</td>
                <td>z,y,x,w,v,u,t,s,r,q,p,o,n,m,l,k,j,i,h,g,f,e,d,c,b,a</td>
                <td>1,2,3,4,5,6,7,8,9,1,11,3,4,5,6,7,8,9,10,2,3,22,5,6,7,8</td>
            </tr>
            <tr>
                <td>Reverse Extended</td>
                <td>z,y,x,w,v,u,t,s,r,q,p,o,n,m,l,k,j,i,h,g,f,e,d,c,b,a</td>
                <td>1,2,3,4,5,6,7,8,9,10,20,30,40,50,60,70,80,90,100,200,300,400,500,600,700,800</td>
            </tr>
        </table>
    </div>
    
    <div class="test">
        <h3>Test Results</h3>
        <div id="testResults"></div>
    </div>

    <div class="test">
        <h3>Manual Calculations</h3>
        <div id="manualCalc"></div>
    </div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function getCipherValue(cipherName, text) {
            for (let i = 0; i < allCiphers.length; i++) {
                if (allCiphers[i].Nickname === cipherName) {
                    return allCiphers[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function manualCalculation(text, cipherType) {
            // Expected mappings from user's rules
            let values = {};
            
            if (cipherType === 'Single Reduction') {
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9,
                    'j': 1, 'k': 2, 'l': 3, 'm': 4, 'n': 5, 'o': 6, 'p': 7, 'q': 8, 'r': 9,
                    's': 10, 't': 2, 'u': 3, 'v': 4, 'w': 5, 'x': 6, 'y': 7, 'z': 8
                };
            } else if (cipherType === 'Single Reduction KV') {
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9,
                    'j': 1, 'k': 11, 'l': 3, 'm': 4, 'n': 5, 'o': 6, 'p': 7, 'q': 8, 'r': 9,
                    's': 10, 't': 2, 'u': 3, 'v': 22, 'w': 5, 'x': 6, 'y': 7, 'z': 8
                };
            } else if (cipherType === 'Extended English') {
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9,
                    'j': 10, 'k': 20, 'l': 30, 'm': 40, 'n': 50, 'o': 60, 'p': 70, 'q': 80, 'r': 90,
                    's': 100, 't': 200, 'u': 300, 'v': 400, 'w': 500, 'x': 600, 'y': 700, 'z': 800
                };
            } else if (cipherType === 'Reverse Single Reduction') {
                // z=1, y=2, x=3, w=4, v=5, u=6, t=7, s=8, r=9, q=1, p=2, o=3, n=4, m=5, l=6, k=7, j=8, i=9, h=10, g=2, f=3, e=4, d=5, c=6, b=7, a=8
                values = {
                    'z': 1, 'y': 2, 'x': 3, 'w': 4, 'v': 5, 'u': 6, 't': 7, 's': 8, 'r': 9,
                    'q': 1, 'p': 2, 'o': 3, 'n': 4, 'm': 5, 'l': 6, 'k': 7, 'j': 8, 'i': 9,
                    'h': 10, 'g': 2, 'f': 3, 'e': 4, 'd': 5, 'c': 6, 'b': 7, 'a': 8
                };
            } else if (cipherType === 'Reverse Single Reduction EP') {
                // z=1, y=2, x=3, w=4, v=5, u=6, t=7, s=8, r=9, q=1, p=11, o=3, n=4, m=5, l=6, k=7, j=8, i=9, h=10, g=2, f=3, e=22, d=5, c=6, b=7, a=8
                values = {
                    'z': 1, 'y': 2, 'x': 3, 'w': 4, 'v': 5, 'u': 6, 't': 7, 's': 8, 'r': 9,
                    'q': 1, 'p': 11, 'o': 3, 'n': 4, 'm': 5, 'l': 6, 'k': 7, 'j': 8, 'i': 9,
                    'h': 10, 'g': 2, 'f': 3, 'e': 22, 'd': 5, 'c': 6, 'b': 7, 'a': 8
                };
            } else if (cipherType === 'Reverse Extended') {
                // z=1, y=2, x=3, w=4, v=5, u=6, t=7, s=8, r=9, q=10, p=20, o=30, n=40, m=50, l=60, k=70, j=80, i=90, h=100, g=200, f=300, e=400, d=500, c=600, b=700, a=800
                values = {
                    'z': 1, 'y': 2, 'x': 3, 'w': 4, 'v': 5, 'u': 6, 't': 7, 's': 8, 'r': 9,
                    'q': 10, 'p': 20, 'o': 30, 'n': 40, 'm': 50, 'l': 60, 'k': 70, 'j': 80, 'i': 90,
                    'h': 100, 'g': 200, 'f': 300, 'e': 400, 'd': 500, 'c': 600, 'b': 700, 'a': 800
                };
            }
            
            let total = 0;
            let breakdown = [];
            
            for (let i = 0; i < text.length; i++) {
                const char = text[i].toLowerCase();
                if (values[char] !== undefined) {
                    total += values[char];
                    breakdown.push(`${text[i]}=${values[char]}`);
                } else if (char === ' ') {
                    breakdown.push('(space)=0');
                }
            }
            
            return { total, breakdown };
        }

        function runTests() {
            const testDiv = document.getElementById('testResults');
            const manualDiv = document.getElementById('manualCalc');
            
            // Test cases
            const testCases = [
                'CAT',    // Simple test
                'HELLO',  // Common test
                'test'    // Case sensitivity test
            ];
            
            const cipherNames = [
                'Single Reduction',
                'Single Reduction KV', 
                'Extended English',
                'Reverse Single Reduction',
                'Reverse Single Reduction EP',
                'Reverse Extended'
            ];
            
            let testResults = '<h4>Test Results:</h4>';
            let manualResults = '<h4>Manual Calculations:</h4>';
            
            for (const testWord of testCases) {
                testResults += `<h5>"${testWord}":</h5>`;
                manualResults += `<h5>"${testWord}":</h5>`;
                
                for (const cipherName of cipherNames) {
                    const myResult = getCipherValue(cipherName, testWord);
                    const manual = manualCalculation(testWord, cipherName);
                    const isCorrect = myResult === manual.total;
                    
                    testResults += `<p class="${isCorrect ? 'correct' : 'incorrect'}">
                        ${cipherName}: ${myResult} (expected: ${manual.total}) ${isCorrect ? '✅' : '❌'}
                    </p>`;
                    
                    manualResults += `<p>${cipherName}: ${manual.breakdown.join('+')} = ${manual.total}</p>`;
                }
                testResults += '<br>';
                manualResults += '<br>';
            }
            
            testDiv.innerHTML = testResults;
            manualDiv.innerHTML = manualResults;
        }

        // Run tests
        runTests();
    </script>
</body>
</html>
