<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Character Arrays</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background-color: #f0f8ff; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Debug Character Arrays</h1>
    
    <div id="debugInfo"></div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function debugCharacterArrays() {
            const debugDiv = document.getElementById('debugInfo');
            let html = '';
            
            // Find Jewish ciphers
            for (let i = 0; i < allCiphers.length; i++) {
                const cipher = allCiphers[i];
                if (cipher.Nickname.includes('Jewish')) {
                    html += `<div class="debug">`;
                    html += `<h3>${cipher.Nickname}</h3>`;
                    html += `<p><strong>isJewish flag:</strong> ${cipher.isJewish}</p>`;
                    html += `<p><strong>cipherName:</strong> ${cipher.cipherName}</p>`;
                    
                    // Character arrays
                    html += `<p><strong>cArr (character codes):</strong> ${cipher.cArr ? cipher.cArr.slice(0, 10).join(',') + '...' : 'undefined'}</p>`;
                    html += `<p><strong>cArr length:</strong> ${cipher.cArr ? cipher.cArr.length : 'undefined'}</p>`;
                    html += `<p><strong>cArr contains ASCII 49 ("1"):</strong> ${cipher.cArr ? cipher.cArr.indexOf(49) : 'N/A'}</p>`;
                    
                    // Value arrays
                    html += `<p><strong>vArr (values):</strong> ${cipher.vArr ? cipher.vArr.slice(0, 10).join(',') + '...' : 'undefined'}</p>`;
                    html += `<p><strong>vArr length:</strong> ${cipher.vArr ? cipher.vArr.length : 'undefined'}</p>`;
                    
                    // Test character lookups
                    const cCode = 67; // 'C'
                    const aCode = 65; // 'A'
                    const tCode = 84; // 'T'
                    
                    html += `<p><strong>Character lookup tests:</strong></p>`;
                    html += `<p>- 'C' (${cCode}): index = ${cipher.cArr ? cipher.cArr.indexOf(cCode) : 'N/A'}</p>`;
                    html += `<p>- 'A' (${aCode}): index = ${cipher.cArr ? cipher.cArr.indexOf(aCode) : 'N/A'}</p>`;
                    html += `<p>- 'T' (${tCode}): index = ${cipher.cArr ? cipher.cArr.indexOf(tCode) : 'N/A'}</p>`;
                    
                    // Test with actual Gematria call
                    const catResult = cipher.Gematria('CAT', 1);
                    html += `<p><strong>Gematria('CAT', 1) result:</strong> ${catResult}</p>`;
                    
                    html += `</div>`;
                }
            }
            
            // ASCII reference
            html += `<div class="debug">`;
            html += `<h3>ASCII Reference</h3>`;
            html += `<p>A=65, B=66, C=67, ..., Z=90</p>`;
            html += `<p>a=97, b=98, c=99, ..., z=122</p>`;
            html += `<p>0=48, 1=49, 2=50, ..., 9=57</p>`;
            html += `<p>Hebrew characters start around 1488</p>`;
            html += `</div>`;
            
            debugDiv.innerHTML = html;
        }

        debugCharacterArrays();
    </script>
</body>
</html>
