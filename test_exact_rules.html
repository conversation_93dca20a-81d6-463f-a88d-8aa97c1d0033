<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Exact Jewish Rules</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .correct { background-color: #e4ffe4; font-weight: bold; }
        .incorrect { background-color: #ffe4e1; font-weight: bold; }
        .manual { background-color: #f0f8ff; padding: 10px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Test Exact Jewish Cipher Rules</h1>
    
    <div class="manual">
        <h3>User's Exact Rules</h3>
        <table>
            <tr>
                <th>Cipher</th>
                <th>Letter Order</th>
                <th>Values</th>
            </tr>
            <tr>
                <td>Jewish Reduction</td>
                <td>a,b,c,d,e,f,g,h,i,k,l,m,n,o,p,q,r,s,t,u,x,y,z,j,v,&,w</td>
                <td>1,2,3,4,5,6,7,8,9,1,2,3,4,5,6,7,8,9,1,2,3,4,5,6,7,8,9</td>
            </tr>
            <tr>
                <td>Jewish Ordinal</td>
                <td>a,b,c,d,e,f,g,h,i,k,l,m,n,o,p,q,r,s,t,u,x,y,z,j,v,&,w</td>
                <td>1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27</td>
            </tr>
            <tr>
                <td>Jewish</td>
                <td>a,b,c,d,e,f,g,h,i,k,l,m,n,o,p,q,r,s,t,u,x,y,z,j,v,&,w</td>
                <td>1,2,3,4,5,6,7,8,9,10,20,30,40,50,60,70,80,90,100,200,300,400,500,600,700,800,900</td>
            </tr>
        </table>
    </div>
    
    <div class="test">
        <h3>Test Results</h3>
        <div id="testResults"></div>
    </div>

    <div class="test">
        <h3>Manual Calculations</h3>
        <div id="manualCalc"></div>
    </div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function getCipherValue(cipherName, text) {
            for (let i = 0; i < allCiphers.length; i++) {
                if (allCiphers[i].Nickname === cipherName) {
                    return allCiphers[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function manualCalculation(text, cipherType) {
            // Exact mappings from user's rules
            let values = {};
            
            if (cipherType === 'reduction') {
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9,
                    'k': 1, 'l': 2, 'm': 3, 'n': 4, 'o': 5, 'p': 6, 'q': 7, 'r': 8, 's': 9,
                    't': 1, 'u': 2, 'x': 3, 'y': 4, 'z': 5, 'j': 6, 'v': 7, '&': 8, 'w': 9
                };
            } else if (cipherType === 'ordinal') {
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9,
                    'k': 10, 'l': 11, 'm': 12, 'n': 13, 'o': 14, 'p': 15, 'q': 16, 'r': 17, 's': 18,
                    't': 19, 'u': 20, 'x': 21, 'y': 22, 'z': 23, 'j': 24, 'v': 25, '&': 26, 'w': 27
                };
            } else { // jewish
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9,
                    'k': 10, 'l': 20, 'm': 30, 'n': 40, 'o': 50, 'p': 60, 'q': 70, 'r': 80, 's': 90,
                    't': 100, 'u': 200, 'x': 300, 'y': 400, 'z': 500, 'j': 600, 'v': 700, '&': 800, 'w': 900
                };
            }
            
            let total = 0;
            let breakdown = [];
            
            for (let i = 0; i < text.length; i++) {
                const char = text[i].toLowerCase();
                if (values[char] !== undefined) {
                    total += values[char];
                    breakdown.push(`${text[i]}=${values[char]}`);
                } else if (char === ' ') {
                    breakdown.push('(space)=0');
                }
            }
            
            return { total, breakdown };
        }

        function runTests() {
            const testDiv = document.getElementById('testResults');
            const manualDiv = document.getElementById('manualCalc');
            
            // Test cases
            const testCases = [
                'CAT',    // C=3, A=1, T=100/1/19
                'HELLO',  // H=8, E=5, L=20/2/11, L=20/2/11, O=50/5/14
                'JEW',    // J=600/6/24, E=5, W=900/9/27
                'LOVE',   // L=20/2/11, O=50/5/14, V=700/7/25, E=5
                'test'    // Test case sensitivity
            ];
            
            let testResults = '<h4>Test Results:</h4>';
            let manualResults = '<h4>Manual Calculations:</h4>';
            
            for (const testWord of testCases) {
                const myJewish = getCipherValue('Jewish', testWord);
                const myReduction = getCipherValue('Jewish Reduction', testWord);
                const myOrdinal = getCipherValue('Jewish Ordinal', testWord);
                
                const manualJewish = manualCalculation(testWord, 'jewish');
                const manualReduction = manualCalculation(testWord, 'reduction');
                const manualOrdinal = manualCalculation(testWord, 'ordinal');
                
                const jewishMatch = myJewish === manualJewish.total;
                const reductionMatch = myReduction === manualReduction.total;
                const ordinalMatch = myOrdinal === manualOrdinal.total;
                
                testResults += `<p><strong>"${testWord}":</strong></p>`;
                testResults += `<p class="${jewishMatch ? 'correct' : 'incorrect'}">
                    Jewish: ${myJewish} (expected: ${manualJewish.total}) ${jewishMatch ? '✅' : '❌'}
                </p>`;
                testResults += `<p class="${reductionMatch ? 'correct' : 'incorrect'}">
                    Jewish Reduction: ${myReduction} (expected: ${manualReduction.total}) ${reductionMatch ? '✅' : '❌'}
                </p>`;
                testResults += `<p class="${ordinalMatch ? 'correct' : 'incorrect'}">
                    Jewish Ordinal: ${myOrdinal} (expected: ${manualOrdinal.total}) ${ordinalMatch ? '✅' : '❌'}
                </p>`;
                testResults += `<br>`;
                
                manualResults += `<p><strong>"${testWord}":</strong></p>`;
                manualResults += `<p>Jewish: ${manualJewish.breakdown.join('+')} = ${manualJewish.total}</p>`;
                manualResults += `<p>Jewish Reduction: ${manualReduction.breakdown.join('+')} = ${manualReduction.total}</p>`;
                manualResults += `<p>Jewish Ordinal: ${manualOrdinal.breakdown.join('+')} = ${manualOrdinal.total}</p>`;
                manualResults += `<br>`;
            }
            
            testDiv.innerHTML = testResults;
            manualDiv.innerHTML = manualResults;
        }

        // Run tests
        runTests();
    </script>
</body>
</html>
