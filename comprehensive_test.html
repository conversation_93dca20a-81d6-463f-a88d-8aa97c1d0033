<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Jewish Cipher Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .mismatch { background-color: #ffe4e1; }
        .match { background-color: #e4ffe4; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .analysis { background-color: #f0f8ff; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Comprehensive Jewish Cipher Analysis</h1>
    
    <div class="test-section">
        <h3>Test Results</h3>
        <table>
            <thead>
                <tr>
                    <th>Input</th>
                    <th>Type</th>
                    <th>Length</th>
                    <th>Code 1 Hebrew Ordinal</th>
                    <th>Code 2 Jewish Ordinal</th>
                    <th>Diff</th>
                    <th>Code 1 Hebrew Gematria</th>
                    <th>Code 2 Jewish</th>
                    <th>Diff</th>
                </tr>
            </thead>
            <tbody id="resultsBody">
            </tbody>
        </table>
    </div>

    <div id="analysisSection" class="analysis">
        <h3>Pattern Analysis</h3>
        <div id="analysisResults"></div>
    </div>

    <!-- Load Code 1 -->
    <script src="Code 1/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        const code1_allCiphers = [...allCiphers];
        allCiphers.length = 0;
    </script>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();
        const code2_allCiphers = [...allCiphers];

        function getCipherValue(cipherList, cipherName, text) {
            for (let i = 0; i < cipherList.length; i++) {
                if (cipherList[i].Nickname === cipherName) {
                    return cipherList[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function addTestResult(input, type, code1Ordinal, code2Ordinal, code1Gematria, code2Gematria) {
            const tbody = document.getElementById('resultsBody');
            const row = tbody.insertRow();
            
            const ordinalDiff = code1Ordinal - code2Ordinal;
            const gematriaDiff = code1Gematria - code2Gematria;
            
            const ordinalMatch = ordinalDiff === 0;
            const gematriaMatch = gematriaDiff === 0;
            
            row.innerHTML = `
                <td>${input.length > 30 ? input.substring(0, 30) + '...' : input}</td>
                <td>${type}</td>
                <td>${input.length}</td>
                <td>${code1Ordinal}</td>
                <td class="${ordinalMatch ? 'match' : 'mismatch'}">${code2Ordinal}</td>
                <td class="${ordinalMatch ? 'match' : 'mismatch'}">${ordinalDiff}</td>
                <td>${code1Gematria}</td>
                <td class="${gematriaMatch ? 'match' : 'mismatch'}">${code2Gematria}</td>
                <td class="${gematriaMatch ? 'match' : 'mismatch'}">${gematriaDiff}</td>
            `;
            
            return { input, type, length: input.length, ordinalDiff, gematriaDiff };
        }

        function runComprehensiveTest() {
            const testCases = [
                // Single characters
                { inputs: ['a', 'e', 's', 't', 'z'], type: 'Single Chars' },
                
                // Short words
                { inputs: ['at', 'be', 'go', 'it', 'me', 'we'], type: 'Short Words' },
                
                // Common words
                { inputs: ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'], type: 'Common Words' },
                
                // Medium words
                { inputs: ['hello', 'world', 'test', 'gematria', 'cipher', 'calculator', 'programming', 'mathematics', 'understanding', 'development'], type: 'Medium Words' },
                
                // Long words
                { inputs: ['antidisestablishmentarianism', 'pneumonoultramicroscopicsilicovolcanoconiosiss', 'supercalifragilisticexpialidocious', 'incomprehensibilities', 'counterrevolutionaries'], type: 'Long Words' },
                
                // Multiple words
                { inputs: ['hello world', 'test case', 'gematria calculator', 'the quick brown fox', 'lorem ipsum dolor sit amet'], type: 'Multiple Words' },
                
                // Sentences
                { inputs: ['This is a test sentence.', 'The quick brown fox jumps over the lazy dog.', 'To be or not to be, that is the question.', 'In the beginning was the Word, and the Word was with God.'], type: 'Sentences' },
                
                // With numbers
                { inputs: ['test123', 'hello2world', 'abc123def', 'year2024', 'version1.0'], type: 'With Numbers' },
                
                // With special characters
                { inputs: ['hello!', 'test?', 'a-b-c', '<EMAIL>', 'file.txt'], type: 'Special Chars' },
                
                // Mixed case
                { inputs: ['Hello', 'WORLD', 'TeSt', 'GeMaTrIa', 'CaLcUlAtOr'], type: 'Mixed Case' },
                
                // Repeated patterns
                { inputs: ['aaa', 'abab', 'abcabc', 'testtest', 'hellohello'], type: 'Repeated' },
                
                // Edge cases
                { inputs: ['', ' ', '  ', '\t', '\n'], type: 'Edge Cases' }
            ];

            const results = [];
            
            for (const testGroup of testCases) {
                for (const input of testGroup.inputs) {
                    const code1Ordinal = getCipherValue(code1_allCiphers, 'Hebrew Ordinal', input);
                    const code2Ordinal = getCipherValue(code2_allCiphers, 'Jewish Ordinal', input);
                    const code1Gematria = getCipherValue(code1_allCiphers, 'Hebrew Gematria', input);
                    const code2Gematria = getCipherValue(code2_allCiphers, 'Jewish', input);
                    
                    const result = addTestResult(input || '(empty)', testGroup.type, code1Ordinal, code2Ordinal, code1Gematria, code2Gematria);
                    results.push(result);
                }
            }
            
            analyzePatterns(results);
        }

        function analyzePatterns(results) {
            const analysisDiv = document.getElementById('analysisResults');
            
            // Find patterns in mismatches
            const mismatches = results.filter(r => r.ordinalDiff !== 0 || r.gematriaDiff !== 0);
            const matches = results.filter(r => r.ordinalDiff === 0 && r.gematriaDiff === 0);
            
            let analysis = `<h4>Summary:</h4>`;
            analysis += `<p>Total tests: ${results.length}</p>`;
            analysis += `<p>Matches: ${matches.length} (${(matches.length/results.length*100).toFixed(1)}%)</p>`;
            analysis += `<p>Mismatches: ${mismatches.length} (${(mismatches.length/results.length*100).toFixed(1)}%)</p>`;
            
            // Analyze mismatch patterns
            const ordinalDiffs = {};
            const gematriaDiffs = {};
            
            mismatches.forEach(m => {
                if (m.ordinalDiff !== 0) {
                    ordinalDiffs[m.ordinalDiff] = (ordinalDiffs[m.ordinalDiff] || 0) + 1;
                }
                if (m.gematriaDiff !== 0) {
                    gematriaDiffs[m.gematriaDiff] = (gematriaDiffs[m.gematriaDiff] || 0) + 1;
                }
            });
            
            analysis += `<h4>Common Difference Patterns:</h4>`;
            analysis += `<p><strong>Ordinal Differences:</strong> ${Object.keys(ordinalDiffs).map(k => `${k} (${ordinalDiffs[k]} times)`).join(', ')}</p>`;
            analysis += `<p><strong>Gematria Differences:</strong> ${Object.keys(gematriaDiffs).map(k => `${k} (${gematriaDiffs[k]} times)`).join(', ')}</p>`;
            
            // Analyze by length
            const lengthAnalysis = {};
            results.forEach(r => {
                const len = r.length;
                if (!lengthAnalysis[len]) lengthAnalysis[len] = { total: 0, mismatches: 0 };
                lengthAnalysis[len].total++;
                if (r.ordinalDiff !== 0 || r.gematriaDiff !== 0) lengthAnalysis[len].mismatches++;
            });
            
            analysis += `<h4>Mismatch Rate by Length:</h4>`;
            Object.keys(lengthAnalysis).sort((a,b) => a-b).forEach(len => {
                const data = lengthAnalysis[len];
                const rate = (data.mismatches / data.total * 100).toFixed(1);
                analysis += `<p>Length ${len}: ${data.mismatches}/${data.total} (${rate}%)</p>`;
            });
            
            analysisDiv.innerHTML = analysis;
        }

        // Run the comprehensive test
        runComprehensiveTest();
    </script>
</body>
</html>
