<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentence Test - Jewish Gematria</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .mismatch { background-color: #ffe4e1; font-weight: bold; }
        .match { background-color: #e4ffe4; font-weight: bold; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .manual-calc { background-color: #f0f8ff; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Sentence Test - Jewish Gematria Analysis</h1>
    
    <div class="test-section">
        <h3>Test Sentences</h3>
        <table>
            <thead>
                <tr>
                    <th>Input</th>
                    <th>Code 1 Hebrew Ordinal</th>
                    <th>Code 2 Jewish Ordinal</th>
                    <th>Match?</th>
                    <th>Code 1 Hebrew Gematria</th>
                    <th>Code 2 Jewish</th>
                    <th>Match?</th>
                </tr>
            </thead>
            <tbody id="resultsBody">
            </tbody>
        </table>
    </div>

    <div class="manual-calc">
        <h3>Manual Calculation with True Jewish Gematria Values</h3>
        <div id="manualCalc"></div>
    </div>

    <!-- Load Code 1 -->
    <script src="Code 1/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        const code1_allCiphers = [...allCiphers];
        allCiphers.length = 0;
    </script>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();
        const code2_allCiphers = [...allCiphers];

        // True Jewish Gematria values from gematrix.org
        const jewishGematriaValues = {
            'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9,
            'j': 600, 'k': 10, 'l': 20, 'm': 30, 'n': 40, 'o': 50, 'p': 60, 'q': 70, 'r': 80, 's': 90, 't': 100,
            'u': 200, 'v': 700, 'w': 900, 'x': 300, 'y': 400, 'z': 500
        };

        function getCipherValue(cipherList, cipherName, text) {
            for (let i = 0; i < cipherList.length; i++) {
                if (cipherList[i].Nickname === cipherName) {
                    return cipherList[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function calculateTrueJewishGematria(text) {
            let total = 0;
            let breakdown = [];
            
            for (let i = 0; i < text.length; i++) {
                const char = text[i].toLowerCase();
                if (jewishGematriaValues[char]) {
                    total += jewishGematriaValues[char];
                    breakdown.push(`${text[i]}=${jewishGematriaValues[char]}`);
                } else if (char === ' ') {
                    breakdown.push('(space)=0');
                } else {
                    breakdown.push(`${text[i]}=?`);
                }
            }
            
            return { total, breakdown };
        }

        function runSentenceTest() {
            const testInputs = [
                'test',
                'hello world',
                'This is a test',
                'The quick brown fox',
                'This is a test sentence',
                'The quick brown fox jumps over the lazy dog',
                'To be or not to be',
                'In the beginning was the Word'
            ];

            const tbody = document.getElementById('resultsBody');
            const manualDiv = document.getElementById('manualCalc');
            
            let manualResults = '<h4>Manual Calculations with True Jewish Gematria:</h4>';

            for (const input of testInputs) {
                const code1Ordinal = getCipherValue(code1_allCiphers, 'Hebrew Ordinal', input);
                const code2Ordinal = getCipherValue(code2_allCiphers, 'Jewish Ordinal', input);
                const code1Gematria = getCipherValue(code1_allCiphers, 'Hebrew Gematria', input);
                const code2Gematria = getCipherValue(code2_allCiphers, 'Jewish', input);
                
                const ordinalMatch = code1Ordinal === code2Ordinal;
                const gematriaMatch = code1Gematria === code2Gematria;
                
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${input}</td>
                    <td>${code1Ordinal}</td>
                    <td class="${ordinalMatch ? 'match' : 'mismatch'}">${code2Ordinal}</td>
                    <td class="${ordinalMatch ? 'match' : 'mismatch'}">${ordinalMatch ? '✅' : '❌'}</td>
                    <td>${code1Gematria}</td>
                    <td class="${gematriaMatch ? 'match' : 'mismatch'}">${code2Gematria}</td>
                    <td class="${gematriaMatch ? 'match' : 'mismatch'}">${gematriaMatch ? '✅' : '❌'}</td>
                `;
                
                // Manual calculation with true Jewish gematria
                const trueJewish = calculateTrueJewishGematria(input);
                manualResults += `<p><strong>"${input}":</strong><br>`;
                manualResults += `Breakdown: ${trueJewish.breakdown.join(' + ')}<br>`;
                manualResults += `True Jewish Gematria: ${trueJewish.total}<br>`;
                manualResults += `Code 1 Hebrew Ordinal: ${code1Ordinal}<br>`;
                manualResults += `Code 1 Hebrew Gematria: ${code1Gematria}<br>`;
                manualResults += `Difference (Ordinal): ${code1Ordinal - trueJewish.total}<br>`;
                manualResults += `Difference (Gematria): ${code1Gematria - trueJewish.total}</p>`;
            }
            
            manualDiv.innerHTML = manualResults;
        }

        // Run the test
        runSentenceTest();
    </script>
</body>
</html>
