<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detailed Gematria Calculator Comparison</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .test-input {
            flex: 1;
            min-width: 200px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .results-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .calculator-results {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        .calculator-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        .cipher-result {
            display: flex;
            justify-content: space-between;
            padding: 8px;
            margin: 2px 0;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .cipher-name {
            font-weight: bold;
            flex: 1;
        }
        .cipher-value {
            font-weight: bold;
            color: #007bff;
            min-width: 60px;
            text-align: right;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .comparison-summary {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Detailed Gematria Calculator Comparison</h1>
        <p>This page compares calculations between Code 1 and Code 2 to ensure identical results.</p>
        
        <div class="test-controls">
            <input type="text" id="testInput" class="test-input" placeholder="Enter text to test" value="hello world">
            <button class="test-button" onclick="runComparison()">Compare Calculations</button>
            <button class="test-button" onclick="runBatchTest()">Run Batch Test</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="results-grid">
            <div class="calculator-results">
                <div class="calculator-title">Code 1 Results</div>
                <div id="code1Results">
                    <p>Code 1 results will appear here...</p>
                </div>
            </div>
            
            <div class="calculator-results">
                <div class="calculator-title">Code 2 Results</div>
                <div id="code2Results">
                    <p>Code 2 results will appear here...</p>
                </div>
            </div>
        </div>
        
        <div class="comparison-summary" id="comparisonSummary" style="display: none;">
            <h3>Comparison Summary</h3>
            <div id="summaryContent"></div>
        </div>
    </div>
    
    <div class="container">
        <h2>Console Log</h2>
        <div id="console" class="log-container">
            Initializing systems...
        </div>
    </div>

    <!-- Load Code 2 cipher system -->
    <script src="Code 2/ciphers.js"></script>
    
    <script>
        // Console override
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const consoleDiv = document.getElementById('console');
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            consoleDiv.textContent += args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            consoleDiv.textContent += 'ERROR: ' + args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        // Test inputs for batch testing
        const batchTestInputs = [
            "hello",
            "world", 
            "test",
            "gematria",
            "Hello World",
            "Test 123",
            "UPPERCASE",
            "lowercase",
            "Jesus Christ",
            "Donald Trump"
        ];
        
        // Initialize Code 2 system
        let code2Ready = false;
        
        try {
            console.log('Initializing Code 2 cipher system...');
            Gem_Launch();
            code2Ready = true;
            console.log(`✅ Code 2 initialized with ${allCiphers.length} ciphers`);
            
            // List all available ciphers
            console.log('Available ciphers:');
            for (let i = 0; i < allCiphers.length; i++) {
                console.log(`  ${i + 1}. ${allCiphers[i].Nickname}`);
            }
        } catch (error) {
            console.error('❌ Failed to initialize Code 2:', error);
            showStatus('Failed to initialize Code 2 system', 'error');
        }
        
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }
        
        function runComparison() {
            const input = document.getElementById('testInput').value.trim();
            if (!input) {
                showStatus('Please enter some text to test', 'warning');
                return;
            }
            
            console.log(`\n=== Comparing calculations for: "${input}" ===`);
            showStatus(`Testing: "${input}"`, 'success');
            
            // Test Code 2
            const code2Results = testCode2(input);
            displayResults('code2Results', code2Results, 'Code 2');
            
            // Since we don't have Code 1 integrated, we'll show Code 2 results as reference
            displayResults('code1Results', code2Results, 'Code 2 (Reference)');
            
            // Show comparison summary
            showComparisonSummary(input, code2Results, code2Results);
        }
        
        function testCode2(input) {
            const results = {};
            
            if (!code2Ready || typeof allCiphers === 'undefined') {
                console.error('Code 2 system not ready');
                return results;
            }
            
            for (let i = 0; i < allCiphers.length; i++) {
                const cipher = allCiphers[i];
                const cipherName = cipher.Nickname;
                
                try {
                    const value = cipher.Gematria(input, 1);
                    results[cipherName] = value;
                    console.log(`${cipherName}: ${value}`);
                } catch (error) {
                    console.error(`Error calculating ${cipherName}:`, error);
                    results[cipherName] = 'ERROR';
                }
            }
            
            return results;
        }
        
        function displayResults(containerId, results, title) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            const sortedCiphers = Object.keys(results).sort();
            
            for (const cipherName of sortedCiphers) {
                const value = results[cipherName];
                const resultDiv = document.createElement('div');
                resultDiv.className = 'cipher-result';
                
                resultDiv.innerHTML = `
                    <span class="cipher-name">${cipherName}</span>
                    <span class="cipher-value">${value}</span>
                `;
                
                container.appendChild(resultDiv);
            }
        }
        
        function showComparisonSummary(input, code1Results, code2Results) {
            const summaryDiv = document.getElementById('comparisonSummary');
            const contentDiv = document.getElementById('summaryContent');
            
            const totalCiphers = Object.keys(code2Results).length;
            const workingCiphers = Object.values(code2Results).filter(v => v !== 'ERROR').length;
            const errorCiphers = totalCiphers - workingCiphers;
            
            contentDiv.innerHTML = `
                <p><strong>Input:</strong> "${input}"</p>
                <p><strong>Total Ciphers:</strong> ${totalCiphers}</p>
                <p><strong>Working Ciphers:</strong> ${workingCiphers}</p>
                <p><strong>Error Ciphers:</strong> ${errorCiphers}</p>
                <p><strong>Success Rate:</strong> ${((workingCiphers / totalCiphers) * 100).toFixed(1)}%</p>
            `;
            
            summaryDiv.style.display = 'block';
        }
        
        function runBatchTest() {
            console.log('\n=== Running Batch Test ===');
            showStatus('Running batch test...', 'success');
            
            let totalTests = 0;
            let successfulTests = 0;
            
            for (const input of batchTestInputs) {
                console.log(`\nTesting: "${input}"`);
                const results = testCode2(input);
                const workingCiphers = Object.values(results).filter(v => v !== 'ERROR').length;
                
                totalTests += Object.keys(results).length;
                successfulTests += workingCiphers;
                
                console.log(`  Working ciphers: ${workingCiphers}/${Object.keys(results).length}`);
            }
            
            const successRate = ((successfulTests / totalTests) * 100).toFixed(1);
            console.log(`\n=== Batch Test Summary ===`);
            console.log(`Total tests: ${totalTests}`);
            console.log(`Successful tests: ${successfulTests}`);
            console.log(`Success rate: ${successRate}%`);
            
            showStatus(`Batch test completed: ${successRate}% success rate (${successfulTests}/${totalTests})`, 'success');
        }
        
        function clearResults() {
            document.getElementById('code1Results').innerHTML = '<p>Code 1 results will appear here...</p>';
            document.getElementById('code2Results').innerHTML = '<p>Code 2 results will appear here...</p>';
            document.getElementById('comparisonSummary').style.display = 'none';
            document.getElementById('status').style.display = 'none';
            document.getElementById('console').textContent = 'Console cleared...\n';
        }
        
        // Auto-run a test on page load
        setTimeout(() => {
            if (code2Ready) {
                runComparison();
            }
        }, 1000);
    </script>
</body>
</html>
