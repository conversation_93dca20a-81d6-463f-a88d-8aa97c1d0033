<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Jewish Ciphers</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background-color: #f0f8ff; padding: 10px; margin: 10px 0; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Debug Jewish Ciphers</h1>
    
    <div class="test">
        <h3>Test "LIFE" with Debug Info</h3>
        <div id="debugTest"></div>
    </div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function getCipherValue(cipherName, text) {
            for (let i = 0; i < allCiphers.length; i++) {
                if (allCiphers[i].Nickname === cipherName) {
                    return allCiphers[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function debugJewishCiphers() {
            const debugDiv = document.getElementById('debugTest');
            
            let debugInfo = '<h4>Jewish Cipher Debug Information:</h4>';
            
            // Find Jewish ciphers and show their properties
            for (let i = 0; i < allCiphers.length; i++) {
                const cipher = allCiphers[i];
                if (cipher.Nickname.includes('Jewish')) {
                    debugInfo += `<div class="debug">`;
                    debugInfo += `<p><strong>${cipher.Nickname}:</strong></p>`;
                    debugInfo += `<p>- isJewish flag: ${cipher.isJewish}</p>`;
                    debugInfo += `<p>- Cipher type: ${cipher.cipherName}</p>`;
                    debugInfo += `<p>- Character array length: ${cipher.cArr ? cipher.cArr.length : 'undefined'}</p>`;
                    debugInfo += `<p>- First few character codes: ${cipher.cArr ? cipher.cArr.slice(0, 5) : 'undefined'}</p>`;
                    debugInfo += `<p>- Value array length: ${cipher.vArr ? cipher.vArr.length : 'undefined'}</p>`;
                    debugInfo += `<p>- First few values: ${cipher.vArr ? cipher.vArr.slice(0, 5) : 'undefined'}</p>`;
                    
                    // Test with single letter
                    const testL = cipher.Gematria('L', 1);
                    const testI = cipher.Gematria('I', 1);
                    const testF = cipher.Gematria('F', 1);
                    const testE = cipher.Gematria('E', 1);
                    
                    debugInfo += `<p>- Test L: ${testL}</p>`;
                    debugInfo += `<p>- Test I: ${testI}</p>`;
                    debugInfo += `<p>- Test F: ${testF}</p>`;
                    debugInfo += `<p>- Test E: ${testE}</p>`;
                    debugInfo += `<p>- Sum L+I+F+E: ${testL + testI + testF + testE}</p>`;
                    
                    // Test with full word
                    const testLIFE = cipher.Gematria('LIFE', 1);
                    debugInfo += `<p>- Test LIFE: ${testLIFE}</p>`;
                    
                    debugInfo += `</div>`;
                }
            }
            
            // Expected values
            debugInfo += `<div class="debug">`;
            debugInfo += `<h4>Expected Values for "LIFE":</h4>`;
            debugInfo += `<p>Jewish: L=30 + I=9 + F=6 + E=5 = 50</p>`;
            debugInfo += `<p>Jewish Reduction: L=3 + I=9 + F=6 + E=5 = 23</p>`;
            debugInfo += `<p>Jewish Ordinal: L=12 + I=9 + F=6 + E=5 = 32</p>`;
            debugInfo += `</div>`;
            
            debugDiv.innerHTML = debugInfo;
        }

        // Run debug
        debugJewishCiphers();
    </script>
</body>
</html>
