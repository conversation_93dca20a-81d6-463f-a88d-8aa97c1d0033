<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Jewish Ciphers</title>
</head>
<body>
    <h1>Debug Jewish Ciphers</h1>
    <div id="results"></div>

    <script src="Code 2/ciphers.js"></script>
    <script>
        // Initialize ciphers
        Set_Categories();
        Build_Ciphers();

        const results = document.getElementById('results');
        results.innerHTML = '<h2>Debug Information</h2>';

        // Test creating a Jewish cipher directly
        try {
            const testCipher = new cipher("Test Jewish", "Jewish", 255, 189, 2);
            results.innerHTML += '<p><strong>Jewish cipher created successfully!</strong></p>';
            results.innerHTML += '<p>Character array length: ' + testCipher.cArr.length + '</p>';
            results.innerHTML += '<p>Value array length: ' + testCipher.vArr.length + '</p>';
            results.innerHTML += '<p>First few chars: ' + testCipher.cArr.slice(0, 5) + '</p>';
            results.innerHTML += '<p>First few values: ' + testCipher.vArr.slice(0, 5) + '</p>';

            // Test calculation
            const testValue = testCipher.Gematria("test", 1);
            results.innerHTML += '<p><strong>Test calculation result: ' + testValue + '</strong></p>';

            // Manual calculation
            let manual = 0;
            const text = "test";
            for (let i = 0; i < text.length; i++) {
                const charCode = text.charCodeAt(i);
                const index = testCipher.cArr.indexOf(charCode);
                if (index > -1) {
                    manual += testCipher.vArr[index];
                    results.innerHTML += '<p>' + text[i] + ' (code ' + charCode + ') at index ' + index + ' = ' + testCipher.vArr[index] + '</p>';
                } else {
                    results.innerHTML += '<p>' + text[i] + ' (code ' + charCode + ') NOT FOUND in cArr</p>';
                    // Try cArr2
                    const index2 = testCipher.cArr2.indexOf(charCode);
                    if (index2 > -1) {
                        manual += testCipher.vArr2[index2];
                        results.innerHTML += '<p>' + text[i] + ' (code ' + charCode + ') found in cArr2 at index ' + index2 + ' = ' + testCipher.vArr2[index2] + '</p>';
                    } else {
                        results.innerHTML += '<p>' + text[i] + ' (code ' + charCode + ') NOT FOUND in cArr2 either</p>';
                    }
                }
            }
            results.innerHTML += '<p><strong>Manual calculation: ' + manual + '</strong></p>';

            // Test with a simple English cipher for comparison
            const englishCipher = new cipher("Test English", "English", 255, 189, 2);
            const englishValue = englishCipher.Gematria("test", 1);
            results.innerHTML += '<p><strong>English cipher test result: ' + englishValue + '</strong></p>';

        } catch (error) {
            results.innerHTML += '<p><strong>Error creating Jewish cipher: ' + error.message + '</strong></p>';
        }

        // Check if Jewish ciphers exist in allCiphers
        results.innerHTML += '<h3>Jewish Ciphers in allCiphers:</h3>';
        for (let i = 0; i < allCiphers.length; i++) {
            if (allCiphers[i].Nickname.includes('Jewish')) {
                results.innerHTML += '<p>' + allCiphers[i].Nickname + ' - Value arrays length: ' + allCiphers[i].vArr.length + '</p>';
            }
        }
    </script>
</body>
</html>
