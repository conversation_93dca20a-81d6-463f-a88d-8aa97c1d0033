<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Jewish Ciphers</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .manual { background-color: #f0f8ff; padding: 10px; margin: 10px 0; }
        .correct { background-color: #e4ffe4; }
        .incorrect { background-color: #ffe4e1; }
    </style>
</head>
<body>
    <h1>Verify Jewish Ciphers Against gematriacalculator.us</h1>
    
    <div class="test-section">
        <h3>Test Words</h3>
        <table>
            <thead>
                <tr>
                    <th>Word</th>
                    <th>Jewish Reduction</th>
                    <th>Jewish Ordinal</th>
                    <th>Jewish</th>
                </tr>
            </thead>
            <tbody id="resultsBody">
            </tbody>
        </table>
    </div>

    <div class="manual">
        <h3>Manual Calculation Examples</h3>
        <div id="manualCalc"></div>
    </div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function getCipherValue(cipherName, text) {
            for (let i = 0; i < allCiphers.length; i++) {
                if (allCiphers[i].Nickname === cipherName) {
                    return allCiphers[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function manualJewishCalculation(text, cipherType) {
            let total = 0;
            let breakdown = [];
            
            // Define the correct values based on gematriacalculator.us
            let values = {};
            
            if (cipherType === 'reduction') {
                // Jewish Reduced: (A-I)=1-9, J=24, (K-S)=1-9, T=1,U=2,V=7,W=9,X=3,Y=4,Z=5
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9,
                    'j': 24, 'k': 1, 'l': 2, 'm': 3, 'n': 4, 'o': 5, 'p': 6, 'q': 7, 'r': 8, 's': 9,
                    't': 1, 'u': 2, 'v': 7, 'w': 9, 'x': 3, 'y': 4, 'z': 5
                };
            } else if (cipherType === 'ordinal') {
                // Jewish Ordinal: Like regular ordinal but J=24, V=25, W=27
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9, 'j': 24,
                    'k': 11, 'l': 12, 'm': 13, 'n': 14, 'o': 15, 'p': 16, 'q': 17, 'r': 18, 's': 19,
                    't': 20, 'u': 21, 'v': 25, 'w': 27, 'x': 22, 'y': 23, 'z': 24
                };
            } else { // main Jewish
                // Jewish: (A-J)=1-9, (K-S)=10-20, (T-Z)=100-500, J=600, V=700, W=900
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9, 'j': 600,
                    'k': 10, 'l': 20, 'm': 30, 'n': 40, 'o': 50, 'p': 60, 'q': 70, 'r': 80, 's': 90,
                    't': 100, 'u': 200, 'v': 700, 'w': 900, 'x': 300, 'y': 400, 'z': 500
                };
            }
            
            for (let i = 0; i < text.length; i++) {
                const char = text[i].toLowerCase();
                if (values[char] !== undefined) {
                    total += values[char];
                    breakdown.push(`${text[i]}=${values[char]}`);
                } else if (char === ' ') {
                    breakdown.push('(space)=0');
                } else {
                    breakdown.push(`${text[i]}=?`);
                }
            }
            
            return { total, breakdown };
        }

        function runVerification() {
            const testWords = [
                'test', 'hello', 'world', 'gematria', 'calculator',
                'a', 'j', 'v', 'w', // special letters
                'hello world', 'this is a test'
            ];

            const tbody = document.getElementById('resultsBody');
            const manualDiv = document.getElementById('manualCalc');
            
            let manualResults = '<h4>Manual Calculations:</h4>';

            for (const word of testWords) {
                const reduction = getCipherValue('Jewish Reduction', word);
                const ordinal = getCipherValue('Jewish Ordinal', word);
                const jewish = getCipherValue('Jewish', word);
                
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${word}</td>
                    <td>${reduction}</td>
                    <td>${ordinal}</td>
                    <td>${jewish}</td>
                `;
                
                // Manual calculations
                const manualReduction = manualJewishCalculation(word, 'reduction');
                const manualOrdinal = manualJewishCalculation(word, 'ordinal');
                const manualJewish = manualJewishCalculation(word, 'main');
                
                manualResults += `<p><strong>"${word}":</strong><br>`;
                manualResults += `Jewish Reduction: ${manualReduction.breakdown.join('+')} = ${manualReduction.total} (Code: ${reduction})<br>`;
                manualResults += `Jewish Ordinal: ${manualOrdinal.breakdown.join('+')} = ${manualOrdinal.total} (Code: ${ordinal})<br>`;
                manualResults += `Jewish: ${manualJewish.breakdown.join('+')} = ${manualJewish.total} (Code: ${jewish})</p>`;
            }
            
            manualDiv.innerHTML = manualResults;
        }

        // Run verification
        runVerification();
    </script>
</body>
</html>
