<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deep Debug Jewish Ciphers</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background-color: #f0f8ff; padding: 10px; margin: 10px 0; font-family: monospace; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Deep Debug Jewish Ciphers</h1>
    
    <div class="test">
        <h3>Debug "CAT" Processing</h3>
        <div id="debugTest"></div>
    </div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        // Override the Gematria method to add debug logging
        function debugGematria(cipher, text) {
            console.log(`\n=== DEBUG ${cipher.Nickname} with "${text}" ===`);
            console.log(`isJewish flag: ${cipher.isJewish}`);
            console.log(`Character array length: ${cipher.cArr ? cipher.cArr.length : 'undefined'}`);
            console.log(`First few character codes: ${cipher.cArr ? cipher.cArr.slice(0, 5) : 'undefined'}`);
            console.log(`Value array length: ${cipher.vArr ? cipher.vArr.length : 'undefined'}`);
            console.log(`First few values: ${cipher.vArr ? cipher.vArr.slice(0, 5) : 'undefined'}`);
            
            // Call the actual Gematria method
            const result = cipher.Gematria(text, 1);
            console.log(`Final result: ${result}`);
            
            return result;
        }

        function runDeepDebug() {
            const debugDiv = document.getElementById('debugTest');
            
            let debugInfo = '<h4>Deep Debug Information:</h4>';
            
            // Find Jewish ciphers
            const jewishCiphers = allCiphers.filter(c => c.Nickname.includes('Jewish'));
            
            for (const cipher of jewishCiphers) {
                debugInfo += `<div class="debug">`;
                debugInfo += `<strong>${cipher.Nickname}:</strong><br>`;
                debugInfo += `isJewish: ${cipher.isJewish}<br>`;
                debugInfo += `cipherName: ${cipher.cipherName}<br>`;
                debugInfo += `Character array: ${cipher.cArr ? cipher.cArr.slice(0, 10).join(',') : 'undefined'}<br>`;
                debugInfo += `Value array: ${cipher.vArr ? cipher.vArr.slice(0, 10).join(',') : 'undefined'}<br>`;
                
                // Test with "CAT"
                console.log(`\n=== Testing ${cipher.Nickname} with "CAT" ===`);
                const result = debugGematria(cipher, 'CAT');
                debugInfo += `Result for "CAT": ${result}<br>`;
                
                debugInfo += `</div>`;
            }
            
            // Expected results
            debugInfo += `<div class="debug">`;
            debugInfo += `<strong>Expected Results for "CAT":</strong><br>`;
            debugInfo += `Jewish: C=3 + A=1 + T=200 = 204<br>`;
            debugInfo += `Jewish Reduction: C=3 + A=1 + T=2 = 6<br>`;
            debugInfo += `Jewish Ordinal: C=3 + A=1 + T=20 = 24<br>`;
            debugInfo += `</div>`;
            
            debugDiv.innerHTML = debugInfo;
            
            // Also check console for detailed logs
            console.log('Check the browser console for detailed debug information');
        }

        // Run debug
        runDeepDebug();
    </script>
</body>
</html>
