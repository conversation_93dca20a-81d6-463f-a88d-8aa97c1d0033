<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cipher Test</title>
</head>
<body>
    <h1>Cipher Test</h1>
    <input type="text" id="testInput" value="test" placeholder="Enter text to test">
    <button onclick="testCiphers()">Test Ciphers</button>
    <div id="results"></div>

    <script src="Code 2/ciphers.js"></script>
    <script>
        // Initialize ciphers
        Set_Categories();
        Build_Ciphers();

        function testCiphers() {
            const text = document.getElementById('testInput').value || 'test';
            const results = document.getElementById('results');
            results.innerHTML = '<h2>Results for: "' + text + '"</h2>';

            // Test specific problematic ciphers
            const testCiphers = [
                'Single Reduction',
                'Single Reduction KV',
                'Extended English',
                'Reverse Single Reduction',
                'Reverse Single Reduction EP',
                'Reverse Extended',
                'Jewish Reduction',
                'Jewish Ordinal',
                'Jewish'
            ];

            for (let i = 0; i < allCiphers.length; i++) {
                const cipher = allCiphers[i];
                if (testCiphers.includes(cipher.Nickname)) {
                    const value = cipher.Gematria(text, 1);

                    // Debug Jewish ciphers
                    if (cipher.Nickname.includes('Jewish')) {
                        console.log('Jewish cipher debug:', cipher.Nickname);
                        console.log('Character arrays:', cipher.cArr, cipher.cArr2);
                        console.log('Value arrays:', cipher.vArr, cipher.vArr2);
                        console.log('Test char "t" code:', 't'.charCodeAt(0));
                        console.log('Index of "t" in cArr:', cipher.cArr.indexOf(116));
                        console.log('Index of "t" in cArr2:', cipher.cArr2.indexOf(84));
                    }

                    results.innerHTML += '<p><strong>' + cipher.Nickname + ':</strong> ' + value + '</p>';
                }
            }
        }

        // Auto-test on load
        testCiphers();
    </script>
</body>
</html>
