<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cipher Test</title>
</head>
<body>
    <h1>Cipher Test</h1>
    <input type="text" id="testInput" value="test" placeholder="Enter text to test">
    <button onclick="testCiphers()">Test Ciphers</button>
    <div id="results"></div>

    <script src="Code 2/ciphers.js"></script>
    <script>
        // Initialize ciphers
        Set_Categories();
        Build_Ciphers();

        function testCiphers() {
            const text = document.getElementById('testInput').value || 'test';
            const results = document.getElementById('results');
            results.innerHTML = '<h2>Results for: "' + text + '"</h2>';

            // Test specific problematic ciphers
            const testCiphers = [
                'Single Reduction',
                'Single Reduction KV',
                'Extended English',
                'Reverse Single Reduction',
                'Reverse Single Reduction EP',
                'Reverse Extended',
                'Jewish Reduction',
                'Jewish Ordinal',
                'Jewish'
            ];

            for (let i = 0; i < allCiphers.length; i++) {
                const cipher = allCiphers[i];
                if (testCiphers.includes(cipher.Nickname)) {
                    const value = cipher.Gematria(text, 1);
                    results.innerHTML += '<p><strong>' + cipher.Nickname + ':</strong> ' + value + '</p>';
                }
            }
        }

        // Auto-test on load
        testCiphers();
    </script>
</body>
</html>
