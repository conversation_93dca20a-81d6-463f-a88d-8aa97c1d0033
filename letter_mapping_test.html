<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Letter Mapping Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Hebrew Cipher Letter Mapping</h1>
    
    <table>
        <thead>
            <tr>
                <th>Letter</th>
                <th>Expected Ordinal</th>
                <th>Code 1 Hebrew Ordinal</th>
                <th>Difference</th>
                <th>Pattern</th>
            </tr>
        </thead>
        <tbody id="mappingTable">
        </tbody>
    </table>

    <!-- Load Code 1 -->
    <script src="Code 1/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        const code1_allCiphers = [...allCiphers];

        function getCipherValue(cipherList, cipherName, text) {
            for (let i = 0; i < cipherList.length; i++) {
                if (cipherList[i].Nickname === cipherName) {
                    return cipherList[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function createLetterMapping() {
            const tbody = document.getElementById('mappingTable');
            const alphabet = 'abcdefghijklmnopqrstuvwxyz';
            
            for (let i = 0; i < alphabet.length; i++) {
                const letter = alphabet[i];
                const expectedOrdinal = i + 1;
                const code1Value = getCipherValue(code1_allCiphers, 'Hebrew Ordinal', letter);
                const difference = code1Value - expectedOrdinal;
                
                let pattern = '';
                if (difference === 0) pattern = 'Same as ordinal';
                else if (difference > 0) pattern = `+${difference}`;
                else pattern = `${difference}`;
                
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${letter.toUpperCase()}</td>
                    <td>${expectedOrdinal}</td>
                    <td>${code1Value}</td>
                    <td>${difference}</td>
                    <td>${pattern}</td>
                `;
            }
            
            // Test some multi-character combinations
            const testWords = ['aa', 'ab', 'abc', 'test', 'hello', 'world', 'gematria', 'tests', 'testing', 'best', 'rest', 'west', 'nest'];
            for (const word of testWords) {
                const expectedSum = word.split('').reduce((sum, char) => {
                    return sum + (char.charCodeAt(0) - 96);
                }, 0);
                const code1Value = getCipherValue(code1_allCiphers, 'Hebrew Ordinal', word);
                const difference = code1Value - expectedSum;
                
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td><strong>${word}</strong></td>
                    <td>${expectedSum}</td>
                    <td>${code1Value}</td>
                    <td>${difference}</td>
                    <td>${difference === 0 ? 'Sum matches' : 'Complex pattern'}</td>
                `;
            }
        }

        createLetterMapping();
    </script>
</body>
</html>
