<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compare Jewish Ciphers - Code 1 vs Code 2</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .comparison { display: flex; gap: 20px; }
        .code-section { flex: 1; border: 1px solid #ccc; padding: 15px; }
        .code1 { background-color: #f0f8ff; }
        .code2 { background-color: #f0fff0; }
        .difference { background-color: #ffe4e1; font-weight: bold; }
        input { padding: 5px; margin: 10px 0; width: 200px; }
        button { padding: 8px 15px; }
    </style>
</head>
<body>
    <h1>Jewish Cipher Comparison: Code 1 vs Code 2</h1>
    
    <div>
        <input type="text" id="testInput" value="test" placeholder="Enter text to test">
        <button onclick="runComparison()">Compare Results</button>
        <button onclick="testSingleLetters()">Test Single Letters</button>
    </div>

    <div class="comparison">
        <div class="code-section code1">
            <h3>Code 1 Results</h3>
            <div id="code1Results">Click "Compare Results" to test</div>
        </div>
        
        <div class="code-section code2">
            <h3>Code 2 Results</h3>
            <div id="code2Results">Click "Compare Results" to test</div>
        </div>
    </div>

    <!-- Load Code 1 -->
    <script src="Code 1/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        // Store Code 1 functions
        const code1_Gem_Launch = Gem_Launch;
        const code1_allCiphers = allCiphers;
        const code1_Set_Categories = Set_Categories;
        const code1_Build_Ciphers = Build_Ciphers;
        
        // Initialize Code 1
        code1_Set_Categories();
        code1_Build_Ciphers();
        const code1Ciphers = [...allCiphers];
        
        // Clear for Code 2
        allCiphers.length = 0;
    </script>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        // Initialize Code 2
        Set_Categories();
        Build_Ciphers();
        const code2Ciphers = [...allCiphers];

        function runComparison() {
            const text = document.getElementById('testInput').value || 'test';

            // Test multiple words to find pattern
            const testWords = [text, 'abc', 'hello', 'test', 'xyz'];

            for (const word of testWords) {
                runSingleComparison(word);
            }
        }

        function runSingleComparison(text) {
            const code1Results = document.getElementById('code1Results');
            const code2Results = document.getElementById('code2Results');

            code1Results.innerHTML += '<h4>Results for: "' + text + '"</h4>';
            code2Results.innerHTML += '<h4>Results for: "' + text + '"</h4>';

            // Cipher names to test
            const testCiphers = [
                'Ordinal',
                'Hebrew Reduction',
                'Hebrew Ordinal',
                'Hebrew Gematria',
                'English Ordinal',
                'Jewish Reduction',
                'Jewish Ordinal',
                'Jewish'
            ];

            // Test Code 1
            code1Results.innerHTML += '<h5>Code 1 Jewish Ciphers:</h5>';
            const code1Values = {};
            for (let i = 0; i < code1Ciphers.length; i++) {
                const cipher = code1Ciphers[i];
                if (testCiphers.includes(cipher.Nickname)) {
                    const value = cipher.Gematria(text, 1);
                    code1Values[cipher.Nickname] = value;

                    // Debug Code 1 cipher
                    if (cipher.Nickname === 'Hebrew Ordinal') {
                        code1Results.innerHTML += '<p><strong>DEBUG ' + cipher.Nickname + ':</strong></p>';
                        code1Results.innerHTML += '<p>Contains ASCII 49 (char "1"): ' + (cipher.cArr.indexOf(49) > -1) + '</p>';

                        // Check if number processing is happening
                        let numberTotal = 0;
                        for (let j = 0; j < text.length; j++) {
                            const charCode = text.charCodeAt(j);
                            if (charCode > 47 && charCode < 58) { // if it's a digit
                                numberTotal += charCode - 48;
                                code1Results.innerHTML += '<p>Number found: ' + text[j] + ' (code ' + charCode + ') = ' + (charCode - 48) + '</p>';
                            }
                        }
                        code1Results.innerHTML += '<p>Number processing total: ' + numberTotal + '</p>';

                        // Manual character calculation
                        let manual = 0;
                        for (let j = 0; j < text.length; j++) {
                            const charCode = text.charCodeAt(j);
                            const index = cipher.cArr.indexOf(charCode);
                            if (index > -1) {
                                manual += cipher.vArr[index];
                                code1Results.innerHTML += '<p>' + text[j] + ' (code ' + charCode + ') at index ' + index + ' = ' + cipher.vArr[index] + '</p>';
                            } else {
                                code1Results.innerHTML += '<p>' + text[j] + ' (code ' + charCode + ') NOT FOUND</p>';
                            }
                        }
                        code1Results.innerHTML += '<p>Character total: ' + manual + '</p>';
                        code1Results.innerHTML += '<p>Expected total (char + num): ' + (manual + numberTotal) + '</p>';
                    }

                    code1Results.innerHTML += '<p><strong>' + cipher.Nickname + ':</strong> ' + value + '</p>';
                }
            }

            // Test Code 2
            code2Results.innerHTML += '<h5>Code 2 Jewish Ciphers:</h5>';
            const code2Values = {};
            for (let i = 0; i < code2Ciphers.length; i++) {
                const cipher = code2Ciphers[i];
                if (testCiphers.includes(cipher.Nickname)) {
                    const value = cipher.Gematria(text, 1);
                    code2Values[cipher.Nickname] = value;
                    
                    // Check if values match
                    const matchingCode1Name = getMatchingCipherName(cipher.Nickname);
                    const matches = code1Values[matchingCode1Name] === value;
                    const className = matches ? '' : 'difference';
                    
                    code2Results.innerHTML += '<p class="' + className + '"><strong>' + cipher.Nickname + ':</strong> ' + value + 
                        (matchingCode1Name && code1Values[matchingCode1Name] !== undefined ? 
                         ' (Code 1 ' + matchingCode1Name + ': ' + code1Values[matchingCode1Name] + ')' : '') + '</p>';
                }
            }
        }

        function getMatchingCipherName(code2Name) {
            const mapping = {
                'Jewish Reduction': 'Hebrew Reduction',
                'Jewish Ordinal': 'Hebrew Ordinal',
                'Jewish': 'Hebrew Gematria'
            };
            return mapping[code2Name] || code2Name;
        }

        function testSingleLetters() {
            const code1Results = document.getElementById('code1Results');
            const code2Results = document.getElementById('code2Results');

            code1Results.innerHTML = '<h4>Single Letter Test</h4>';
            code2Results.innerHTML = '<h4>Single Letter Test</h4>';

            const letters = ['a', 'b', 'c', 't', 'e', 's'];

            for (const letter of letters) {
                // Test Code 1 Hebrew Ordinal
                let code1Value = 0;
                for (let i = 0; i < code1Ciphers.length; i++) {
                    if (code1Ciphers[i].Nickname === 'Hebrew Ordinal') {
                        code1Value = code1Ciphers[i].Gematria(letter, 1);
                        break;
                    }
                }

                // Test Code 2 Jewish Ordinal
                let code2Value = 0;
                for (let i = 0; i < code2Ciphers.length; i++) {
                    if (code2Ciphers[i].Nickname === 'Jewish Ordinal') {
                        code2Value = code2Ciphers[i].Gematria(letter, 1);
                        break;
                    }
                }

                const ordinalValue = letter.charCodeAt(0) - 96; // a=1, b=2, etc.
                code1Results.innerHTML += '<p>' + letter + ': ' + code1Value + ' (ordinal: ' + ordinalValue + ', diff: ' + (code1Value - ordinalValue) + ')</p>';
                code2Results.innerHTML += '<p>' + letter + ': ' + code2Value + ' (ordinal: ' + ordinalValue + ', diff: ' + (code2Value - ordinalValue) + ')</p>';
            }
        }

        // Auto-run comparison on load
        runComparison();
    </script>
</body>
</html>
