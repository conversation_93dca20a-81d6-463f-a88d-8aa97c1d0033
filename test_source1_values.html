<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Source 1 Jewish Ciphers</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .correct { background-color: #e4ffe4; font-weight: bold; }
        .incorrect { background-color: #ffe4e1; font-weight: bold; }
        .manual { background-color: #f0f8ff; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Jewish Ciphers with Source 1 Values</h1>
    
    <div class="test">
        <h3>Source 1 Examples</h3>
        <div id="source1Tests"></div>
    </div>

    <div class="manual">
        <h3>Expected Values from Source 1</h3>
        <div id="expectedValues"></div>
    </div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function getCipherValue(cipherName, text) {
            for (let i = 0; i < allCiphers.length; i++) {
                if (allCiphers[i].Nickname === cipherName) {
                    return allCiphers[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function manualCalculation(text, cipherType) {
            // Source 1 values
            let values = {};
            
            if (cipherType === 'jewish') {
                // Jewish Gematria from Source 1
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9, 'j': 10,
                    'k': 20, 'l': 30, 'm': 40, 'n': 50, 'o': 60, 'p': 70, 'q': 80, 'r': 90, 's': 100,
                    't': 200, 'u': 300, 'v': 400, 'w': 500, 'x': 600, 'y': 700, 'z': 800
                };
            } else if (cipherType === 'reduction') {
                // Jewish Reduction from Source 1 (reduced values)
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9,
                    'j': 1, 'k': 2, 'l': 3, 'm': 4, 'n': 5, 'o': 6, 'p': 7, 'q': 8, 'r': 9,
                    's': 1, 't': 2, 'u': 3, 'v': 4, 'w': 5, 'x': 6, 'y': 7, 'z': 8
                };
            } else { // ordinal
                // Jewish Ordinal from Source 1
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9, 'j': 10,
                    'k': 11, 'l': 12, 'm': 13, 'n': 14, 'o': 15, 'p': 16, 'q': 17, 'r': 18, 's': 19,
                    't': 20, 'u': 21, 'v': 22, 'w': 23, 'x': 24, 'y': 25, 'z': 26
                };
            }
            
            let total = 0;
            let breakdown = [];
            
            for (let i = 0; i < text.length; i++) {
                const char = text[i].toLowerCase();
                if (values[char] !== undefined) {
                    total += values[char];
                    breakdown.push(`${text[i]}=${values[char]}`);
                } else if (char === ' ') {
                    breakdown.push('(space)=0');
                }
            }
            
            return { total, breakdown };
        }

        function runTests() {
            const source1Div = document.getElementById('source1Tests');
            const expectedDiv = document.getElementById('expectedValues');
            
            // Test examples from Source 1
            const testCases = [
                { word: 'CAT', expectedJewish: 204, expectedReduction: 6, expectedOrdinal: 24 },
                { word: 'LOVE', expectedJewish: 495, expectedReduction: 18, expectedOrdinal: 54 },
                { word: 'HELLO', expectedJewish: 133, expectedReduction: 25, expectedOrdinal: 52 }
            ];
            
            let source1Results = '<h4>Test Results:</h4>';
            let expectedResults = '<h4>Manual Calculations:</h4>';
            
            for (const testCase of testCases) {
                const myJewish = getCipherValue('Jewish', testCase.word);
                const myReduction = getCipherValue('Jewish Reduction', testCase.word);
                const myOrdinal = getCipherValue('Jewish Ordinal', testCase.word);
                
                const jewishMatch = myJewish === testCase.expectedJewish;
                const reductionMatch = myReduction === testCase.expectedReduction;
                const ordinalMatch = myOrdinal === testCase.expectedOrdinal;
                
                source1Results += `<p><strong>"${testCase.word}":</strong></p>`;
                source1Results += `<p class="${jewishMatch ? 'correct' : 'incorrect'}">
                    Jewish: ${myJewish} (expected: ${testCase.expectedJewish}) ${jewishMatch ? '✅' : '❌'}
                </p>`;
                source1Results += `<p class="${reductionMatch ? 'correct' : 'incorrect'}">
                    Jewish Reduction: ${myReduction} (expected: ${testCase.expectedReduction}) ${reductionMatch ? '✅' : '❌'}
                </p>`;
                source1Results += `<p class="${ordinalMatch ? 'correct' : 'incorrect'}">
                    Jewish Ordinal: ${myOrdinal} (expected: ${testCase.expectedOrdinal}) ${ordinalMatch ? '✅' : '❌'}
                </p>`;
                
                // Manual calculations
                const manualJewish = manualCalculation(testCase.word, 'jewish');
                const manualReduction = manualCalculation(testCase.word, 'reduction');
                const manualOrdinal = manualCalculation(testCase.word, 'ordinal');
                
                expectedResults += `<p><strong>"${testCase.word}":</strong></p>`;
                expectedResults += `<p>Jewish: ${manualJewish.breakdown.join('+')} = ${manualJewish.total}</p>`;
                expectedResults += `<p>Jewish Reduction: ${manualReduction.breakdown.join('+')} = ${manualReduction.total}</p>`;
                expectedResults += `<p>Jewish Ordinal: ${manualOrdinal.breakdown.join('+')} = ${manualOrdinal.total}</p>`;
                expectedResults += `<br>`;
            }
            
            source1Div.innerHTML = source1Results;
            expectedDiv.innerHTML = expectedResults;
        }

        // Run tests
        runTests();
    </script>
</body>
</html>
