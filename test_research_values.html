<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Research Values</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .correct { background-color: #e4ffe4; font-weight: bold; }
        .incorrect { background-color: #ffe4e1; font-weight: bold; }
        .manual { background-color: #f0f8ff; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Jewish Ciphers with Research Values</h1>
    
    <div class="test">
        <h3>Example from Research: "LIFE"</h3>
        <div id="lifeTest"></div>
    </div>

    <div class="test">
        <h3>Additional Tests</h3>
        <div id="additionalTests"></div>
    </div>

    <div class="manual">
        <h3>Manual Calculations</h3>
        <div id="manualCalc"></div>
    </div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function getCipherValue(cipherName, text) {
            for (let i = 0; i < allCiphers.length; i++) {
                if (allCiphers[i].Nickname === cipherName) {
                    return allCiphers[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function manualJewishCalculation(text, cipherType) {
            // Values from your research
            let values = {};
            
            if (cipherType === 'reduction') {
                // Jewish Reduction: A=1, B=2, C=3, D=4, E=5, F=6, G=7, H=8, I=9, J=1, K=2, L=3, M=4, N=5, O=6, P=7, Q=8, R=9, S=1, T=2, U=3, V=4, W=5, X=6, Y=7, Z=8
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9,
                    'j': 1, 'k': 2, 'l': 3, 'm': 4, 'n': 5, 'o': 6, 'p': 7, 'q': 8, 'r': 9,
                    's': 1, 't': 2, 'u': 3, 'v': 4, 'w': 5, 'x': 6, 'y': 7, 'z': 8
                };
            } else if (cipherType === 'ordinal') {
                // Jewish Ordinal: A=1, B=2, ..., Z=26
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9, 'j': 10,
                    'k': 11, 'l': 12, 'm': 13, 'n': 14, 'o': 15, 'p': 16, 'q': 17, 'r': 18, 's': 19,
                    't': 20, 'u': 21, 'v': 22, 'w': 23, 'x': 24, 'y': 25, 'z': 26
                };
            } else { // main Jewish
                // Jewish: A=1, B=2, C=3, D=4, E=5, F=6, G=7, H=8, I=9, J=10, K=20, L=30, M=40, N=50, O=60, P=70, Q=80, R=90, S=100, T=200, U=300, V=400, W=500, X=600, Y=700, Z=800
                values = {
                    'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9, 'j': 10,
                    'k': 20, 'l': 30, 'm': 40, 'n': 50, 'o': 60, 'p': 70, 'q': 80, 'r': 90, 's': 100,
                    't': 200, 'u': 300, 'v': 400, 'w': 500, 'x': 600, 'y': 700, 'z': 800
                };
            }
            
            let total = 0;
            let breakdown = [];
            
            for (let i = 0; i < text.length; i++) {
                const char = text[i].toLowerCase();
                if (values[char] !== undefined) {
                    total += values[char];
                    breakdown.push(`${text[i]}=${values[char]}`);
                } else if (char === ' ') {
                    breakdown.push('(space)=0');
                }
            }
            
            return { total, breakdown };
        }

        function runTests() {
            const lifeDiv = document.getElementById('lifeTest');
            const additionalDiv = document.getElementById('additionalTests');
            const manualDiv = document.getElementById('manualCalc');
            
            // Test "LIFE" from research
            const lifeReduction = getCipherValue('Jewish Reduction', 'LIFE');
            const lifeOrdinal = getCipherValue('Jewish Ordinal', 'LIFE');
            const lifeJewish = getCipherValue('Jewish', 'LIFE');
            
            // Expected values from research
            const expectedReduction = 23; // L=3, I=9, F=6, E=5
            const expectedOrdinal = 32;   // L=12, I=9, F=6, E=5
            const expectedJewish = 50;    // L=30, I=9, F=6, E=5
            
            lifeDiv.innerHTML = `
                <p><strong>Word: "LIFE"</strong></p>
                <p class="${lifeReduction === expectedReduction ? 'correct' : 'incorrect'}">
                    Jewish Reduction: ${lifeReduction} (expected: ${expectedReduction}) ${lifeReduction === expectedReduction ? '✅' : '❌'}
                </p>
                <p class="${lifeOrdinal === expectedOrdinal ? 'correct' : 'incorrect'}">
                    Jewish Ordinal: ${lifeOrdinal} (expected: ${expectedOrdinal}) ${lifeOrdinal === expectedOrdinal ? '✅' : '❌'}
                </p>
                <p class="${lifeJewish === expectedJewish ? 'correct' : 'incorrect'}">
                    Jewish: ${lifeJewish} (expected: ${expectedJewish}) ${lifeJewish === expectedJewish ? '✅' : '❌'}
                </p>
            `;
            
            // Additional tests
            const testWords = ['test', 'hello', 'world'];
            let additionalResults = '<h4>Additional Test Words:</h4>';
            
            for (const word of testWords) {
                const reduction = getCipherValue('Jewish Reduction', word);
                const ordinal = getCipherValue('Jewish Ordinal', word);
                const jewish = getCipherValue('Jewish', word);
                
                additionalResults += `<p><strong>"${word}":</strong> Reduction=${reduction}, Ordinal=${ordinal}, Jewish=${jewish}</p>`;
            }
            
            additionalDiv.innerHTML = additionalResults;
            
            // Manual calculations
            let manualResults = '<h4>Manual Calculations for "LIFE":</h4>';
            
            const manualReduction = manualJewishCalculation('LIFE', 'reduction');
            const manualOrdinal = manualJewishCalculation('LIFE', 'ordinal');
            const manualJewish = manualJewishCalculation('LIFE', 'main');
            
            manualResults += `<p><strong>Jewish Reduction:</strong> ${manualReduction.breakdown.join('+')} = ${manualReduction.total}</p>`;
            manualResults += `<p><strong>Jewish Ordinal:</strong> ${manualOrdinal.breakdown.join('+')} = ${manualOrdinal.total}</p>`;
            manualResults += `<p><strong>Jewish:</strong> ${manualJewish.breakdown.join('+')} = ${manualJewish.total}</p>`;
            
            manualDiv.innerHTML = manualResults;
        }

        // Run tests
        runTests();
    </script>
</body>
</html>
