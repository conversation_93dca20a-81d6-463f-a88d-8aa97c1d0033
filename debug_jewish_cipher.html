<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Jewish Cipher +9 Issue</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; }
        .debug { background-color: #fff3cd; padding: 10px; margin: 10px 0; }
        .error { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>Debug Jewish Cipher +9 Issue</h1>
    
    <div class="test-section">
        <h3>Letter-by-Letter Analysis</h3>
        <table>
            <thead>
                <tr>
                    <th>Letter</th>
                    <th>Expected Jewish Ordinal</th>
                    <th>My Code Result</th>
                    <th>Difference</th>
                </tr>
            </thead>
            <tbody id="letterTable">
            </tbody>
        </table>
    </div>

    <div class="debug">
        <h3>Debug Information</h3>
        <div id="debugInfo"></div>
    </div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function getCipherValue(cipherName, text) {
            for (let i = 0; i < allCiphers.length; i++) {
                if (allCiphers[i].Nickname === cipherName) {
                    return allCiphers[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function getExpectedJewishOrdinal(char) {
            // Based on gematriacalculator.us documentation
            const values = {
                'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7, 'h': 8, 'i': 9, 'j': 24,
                'k': 11, 'l': 12, 'm': 13, 'n': 14, 'o': 15, 'p': 16, 'q': 17, 'r': 18, 's': 19,
                't': 20, 'u': 21, 'v': 25, 'w': 27, 'x': 22, 'y': 23, 'z': 25
            };
            return values[char.toLowerCase()] || 0;
        }

        function debugLetters() {
            const alphabet = 'abcdefghijklmnopqrstuvwxyz';
            const tbody = document.getElementById('letterTable');
            const debugDiv = document.getElementById('debugInfo');
            
            let debugInfo = '<h4>Detailed Analysis:</h4>';
            let totalOrdinalDiff = 0;
            let errorLetters = [];
            
            for (let i = 0; i < alphabet.length; i++) {
                const letter = alphabet[i];
                
                const expectedOrdinal = getExpectedJewishOrdinal(letter);
                const myOrdinal = getCipherValue('Jewish Ordinal', letter);
                const ordinalDiff = myOrdinal - expectedOrdinal;
                
                totalOrdinalDiff += ordinalDiff;
                
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${letter.toUpperCase()}</td>
                    <td>${expectedOrdinal}</td>
                    <td class="${ordinalDiff !== 0 ? 'error' : ''}">${myOrdinal}</td>
                    <td class="${ordinalDiff !== 0 ? 'error' : ''}">${ordinalDiff}</td>
                `;
                
                if (ordinalDiff !== 0) {
                    errorLetters.push(`${letter.toUpperCase()}: Expected ${expectedOrdinal}, Got ${myOrdinal}, Diff: ${ordinalDiff}`);
                }
            }
            
            debugInfo += `<p><strong>Total difference across all letters:</strong> ${totalOrdinalDiff}</p>`;
            debugInfo += `<p><strong>Letters with errors:</strong></p><ul>`;
            errorLetters.forEach(error => {
                debugInfo += `<li>${error}</li>`;
            });
            debugInfo += '</ul>';
            
            // Test some words
            const testWords = ['test', 'hello', 'world'];
            debugInfo += '<h4>Word Tests:</h4>';
            
            for (const word of testWords) {
                const expectedOrdinal = word.split('').reduce((sum, char) => sum + getExpectedJewishOrdinal(char), 0);
                const myOrdinal = getCipherValue('Jewish Ordinal', word);
                const ordinalDiff = myOrdinal - expectedOrdinal;
                
                debugInfo += `<p><strong>"${word}":</strong> Expected ${expectedOrdinal}, Got ${myOrdinal}, Diff: ${ordinalDiff}</p>`;
            }
            
            debugDiv.innerHTML = debugInfo;
        }

        // Run debug analysis
        debugLetters();
    </script>
</body>
</html>
