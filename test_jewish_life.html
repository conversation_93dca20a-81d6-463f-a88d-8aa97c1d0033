<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Jewish Ciphers with LIFE</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .correct { background-color: #e4ffe4; font-weight: bold; }
        .incorrect { background-color: #ffe4e1; font-weight: bold; }
        .manual { background-color: #f0f8ff; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Jewish Ciphers with "LIFE"</h1>
    
    <div class="test">
        <h3>Test Results for "LIFE"</h3>
        <div id="lifeTest"></div>
    </div>

    <div class="manual">
        <h3>Expected Values from Research</h3>
        <div id="expectedValues"></div>
    </div>

    <div class="test">
        <h3>Additional Tests</h3>
        <div id="additionalTests"></div>
    </div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function getCipherValue(cipherName, text) {
            for (let i = 0; i < allCiphers.length; i++) {
                if (allCiphers[i].Nickname === cipherName) {
                    return allCiphers[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function runTests() {
            const lifeDiv = document.getElementById('lifeTest');
            const expectedDiv = document.getElementById('expectedValues');
            const additionalDiv = document.getElementById('additionalTests');
            
            // Test "LIFE"
            const lifeJewish = getCipherValue('Jewish', 'LIFE');
            const lifeReduction = getCipherValue('Jewish Reduction', 'LIFE');
            const lifeOrdinal = getCipherValue('Jewish Ordinal', 'LIFE');
            
            // Expected values from research
            const expectedJewish = 50;    // L=30, I=9, F=6, E=5
            const expectedReduction = 23; // L=3, I=9, F=6, E=5
            const expectedOrdinal = 32;   // L=12, I=9, F=6, E=5
            
            lifeDiv.innerHTML = `
                <p><strong>Word: "LIFE"</strong></p>
                <p class="${lifeJewish === expectedJewish ? 'correct' : 'incorrect'}">
                    Jewish: ${lifeJewish} (expected: ${expectedJewish}) ${lifeJewish === expectedJewish ? '✅' : '❌'}
                </p>
                <p class="${lifeReduction === expectedReduction ? 'correct' : 'incorrect'}">
                    Jewish Reduction: ${lifeReduction} (expected: ${expectedReduction}) ${lifeReduction === expectedReduction ? '✅' : '❌'}
                </p>
                <p class="${lifeOrdinal === expectedOrdinal ? 'correct' : 'incorrect'}">
                    Jewish Ordinal: ${lifeOrdinal} (expected: ${expectedOrdinal}) ${lifeOrdinal === expectedOrdinal ? '✅' : '❌'}
                </p>
            `;
            
            // Show expected calculations
            expectedDiv.innerHTML = `
                <h4>Manual Calculations from Research:</h4>
                <p><strong>Jewish Cipher:</strong> L=30 + I=9 + F=6 + E=5 = 50</p>
                <p><strong>Jewish Reduction:</strong> L=3 + I=9 + F=6 + E=5 = 23</p>
                <p><strong>Jewish Ordinal:</strong> L=12 + I=9 + F=6 + E=5 = 32</p>
                
                <h4>Letter Value Mappings:</h4>
                <p><strong>Jewish:</strong> A=1, B=2, C=3, D=4, E=5, F=6, G=7, H=8, I=9, J=10, K=20, L=30, M=40, N=50, O=60, P=70, Q=80, R=90, S=100, T=200, U=300, V=400, W=500, X=600, Y=700, Z=800</p>
                <p><strong>Jewish Reduction:</strong> A=1, B=2, C=3, D=4, E=5, F=6, G=7, H=8, I=9, J=1, K=2, L=3, M=4, N=5, O=6, P=7, Q=8, R=9, S=1, T=2, U=3, V=4, W=5, X=6, Y=7, Z=8</p>
                <p><strong>Jewish Ordinal:</strong> A=1, B=2, C=3, ..., Z=26</p>
            `;
            
            // Additional tests
            const testWords = ['test', 'hello', 'world', 'gematria'];
            let additionalResults = '<h4>Additional Test Words:</h4>';
            
            for (const word of testWords) {
                const jewish = getCipherValue('Jewish', word);
                const reduction = getCipherValue('Jewish Reduction', word);
                const ordinal = getCipherValue('Jewish Ordinal', word);
                
                additionalResults += `<p><strong>"${word}":</strong> Jewish=${jewish}, Reduction=${reduction}, Ordinal=${ordinal}</p>`;
            }
            
            additionalDiv.innerHTML = additionalResults;
        }

        // Run tests
        runTests();
    </script>
</body>
</html>
