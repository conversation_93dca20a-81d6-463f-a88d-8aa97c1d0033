/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #2c3e50;
    color: white;
    border-radius: 5px;
}

header h1 {
    margin-bottom: 10px;
}

/* Main Content Layout */
.main-content {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    flex-shrink: 0;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    height: fit-content;
    max-height: 80vh;
    overflow-y: auto;
}

.left-sidebar {
    width: 250px;
}

.right-sidebar {
    width: 300px;
}

.filter-section h3, .summary-section h3 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #2c3e50;
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.filter-actions button {
    flex: 1;
    padding: 8px;
    font-size: 14px;
}

.cipher-filters {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 60vh;
    overflow-y: auto;
}

.cipher-filter-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.cipher-filter-item:hover {
    background-color: #f0f7ff;
}

.cipher-filter-item input {
    margin-right: 10px;
    cursor: pointer;
}

.cipher-filter-item label {
    cursor: pointer;
    font-size: 14px;
    flex: 1;
}

/* Category Headers for Filters */
.cipher-category-header {
    font-weight: bold;
    font-size: 14px;
    color: #2c3e50;
    padding: 8px 10px;
    margin: 10px 0 5px 0;
    background-color: #ecf0f1;
    border-radius: 4px;
    cursor: pointer;
    user-select: none;
    border-left: 3px solid #3498db;
}

.cipher-category-header:hover {
    background-color: #d5dbdb;
}

.cipher-category-header.collapsed::after {
    content: " (collapsed)";
    font-size: 12px;
    color: #7f8c8d;
}

.cipher-category-items {
    margin-bottom: 10px;
}

/* Summary Styles */
#summary-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 70vh;
    overflow-y: auto;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
    border-left: 3px solid #3498db;
    font-size: 14px;
    transition: background-color 0.2s;
}

.summary-item:hover {
    background-color: #f0f7ff;
}

.summary-name {
    font-weight: 500;
}

.summary-values {
    font-weight: bold;
    color: #2c3e50;
}

/* Summary Category Headers */
.summary-category-header {
    font-weight: bold;
    font-size: 14px;
    color: #2c3e50;
    padding: 8px 10px;
    margin: 10px 0 5px 0;
    background-color: #ecf0f1;
    border-radius: 4px;
    cursor: pointer;
    user-select: none;
    border-left: 3px solid #3498db;
}

.summary-category-header:hover {
    background-color: #d5dbdb;
}

.summary-category-header.collapsed::after {
    content: " (collapsed)";
    font-size: 12px;
    color: #7f8c8d;
}

.summary-category-items {
    margin-bottom: 10px;
}

/* Main Content Styles */
main {
    flex: 1;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.input-section {
    margin-bottom: 20px;
}

textarea {
    width: 100%;
    height: 100px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    resize: vertical;
    margin-bottom: 10px;
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

.results-section h2 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.empty-result {
    color: #999;
    font-style: italic;
}

/* Results Display Styles */
.cipher-result {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border-left: 4px solid #3498db;
}

/* Result Category Headers */
.result-category-header {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    margin: 25px 0 15px 0;
    padding: 10px 0;
    border-bottom: 2px solid #3498db;
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
    padding-left: 15px;
    border-radius: 4px;
}

.result-category-header:first-child {
    margin-top: 0;
}

.result-category-container {
    margin-bottom: 20px;
}

.cipher-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    align-items: center;
}

.cipher-name {
    font-weight: bold;
    font-size: 18px;
}

.cipher-value {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    min-width: 60px;
    text-align: right;
}

/* New Clean Result Styles */
.cipher-result-new {
    margin-bottom: 25px;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.cipher-header-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f1f3f4;
}

.cipher-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.cipher-name {
    font-weight: bold;
    font-size: 1.3em;
    color: #2c3e50;
}

.cipher-category-tag {
    font-size: 0.85em;
    color: #3498db;
    background-color: #ecf0f1;
    padding: 4px 10px;
    border-radius: 15px;
    font-weight: 500;
}

.cipher-total {
    font-weight: bold;
    font-size: 1.8em;
    color: #27ae60;
}

/* Word Breakdown Section */
.word-breakdown-section {
    margin-bottom: 25px;
}

.word-breakdown-title {
    font-weight: bold;
    color: #34495e;
    margin-bottom: 12px;
    font-size: 1.1em;
}

.word-breakdown-item {
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #3498db;
}

.word-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.word-name {
    font-weight: bold;
    color: #2c3e50;
    font-size: 1.1em;
}

.word-total {
    font-weight: bold;
    color: #3498db;
    font-size: 1.2em;
}

.word-letters {
    font-family: 'Courier New', monospace;
    color: #7f8c8d;
    font-size: 0.95em;
    line-height: 1.4;
}

/* Word Letter Grid */
.word-letter-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 10px;
    padding: 10px;
    background-color: #ffffff;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.word-letter-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 6px 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    min-width: 30px;
    min-height: 35px;
}

.word-letter {
    font-weight: bold;
    font-size: 1em;
    color: #2c3e50;
    margin-bottom: 2px;
}

.word-letter-value {
    font-size: 0.8em;
    color: #3498db;
    font-weight: bold;
}

/* Cipher Alphabet Section */
.cipher-alphabet-section {
    margin-top: 20px;
}

.cipher-alphabet-title {
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 0;
    font-size: 1.2em;
    background-color: #2c3e50;
    padding: 12px 15px;
    border-radius: 8px 8px 0 0;
    border: 2px solid #34495e;
    border-bottom: none;
}

.letter-grid {
    display: grid;
    grid-template-columns: repeat(13, 1fr);
    gap: 6px;
    padding: 15px;
    background-color: #2c3e50;
    border-radius: 0 0 8px 8px;
    border: 2px solid #34495e;
    border-top: none;
    overflow-x: auto;
    min-height: 120px;
}

.letter-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 4px;
    background-color: #34495e;
    border-radius: 4px;
    border: 1px solid #4a5568;
    min-width: 35px;
    min-height: 45px;
    transition: background-color 0.2s;
}

.letter-cell:hover {
    background-color: #4a5568;
}

.letter {
    font-weight: bold;
    font-size: 1em;
    color: #ffffff;
    margin-bottom: 2px;
}

.letter-value {
    font-size: 0.85em;
    color: #3498db;
    font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .letter-grid {
        grid-template-columns: repeat(auto-fit, minmax(35px, 1fr));
        gap: 6px;
        padding: 10px;
    }

    .letter-cell {
        padding: 6px 2px;
        min-width: 30px;
    }

    .cipher-header-new {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .cipher-total {
        align-self: flex-end;
    }
}

.cipher-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.word-breakdown {
    margin-top: 15px;
}

.word-breakdown h4 {
    margin-bottom: 15px;
    font-size: 16px;
    color: #555;
}

.word-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-start;
}

.word-item {
    background-color: #e8f4fc;
    padding: 12px 15px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.word-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding-bottom: 5px;
}

.word-text {
    font-weight: 500;
}

.word-value {
    font-weight: bold;
    color: #3498db;
}

.letter-breakdown {
    font-family: monospace;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

/* Footer Styles */
footer {
    text-align: center;
    padding: 10px;
    color: #666;
    font-size: 14px;
}

/* Grid Layout for Results */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Hide Calculate Button when Auto-Calculate is enabled */
.hidden {
    display: none;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .main-content {
        flex-wrap: wrap;
    }

    main {
        order: 1;
        width: 100%;
    }

    .left-sidebar {
        order: 2;
        width: calc(50% - 10px);
    }

    .right-sidebar {
        order: 3;
        width: calc(50% - 10px);
    }
}

@media (max-width: 900px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: none;
    }

    .cipher-filters, #summary-container {
        max-height: 300px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header, main {
        padding: 15px;
    }

    .cipher-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .word-list {
        flex-direction: column;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }
}

/* Add this to your existing CSS */
.search-box {
    margin-bottom: 15px;
}

.search-box input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}