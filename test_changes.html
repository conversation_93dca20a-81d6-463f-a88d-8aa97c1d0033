<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Jewish Cipher Changes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .debug { background-color: #f0f8ff; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Jewish Cipher Changes</h1>
    
    <div class="test">
        <h3>Simple Test: Letter 'a'</h3>
        <div id="letterTest"></div>
    </div>

    <div class="test">
        <h3>Word Test: 'test'</h3>
        <div id="wordTest"></div>
    </div>

    <div class="debug">
        <h3>Debug Information</h3>
        <div id="debugInfo"></div>
    </div>

    <!-- Load Code 2 -->
    <script src="Code 2/ciphers.js"></script>
    <script>
        Set_Categories();
        Build_Ciphers();

        function getCipherValue(cipherName, text) {
            for (let i = 0; i < allCiphers.length; i++) {
                if (allCiphers[i].Nickname === cipherName) {
                    return allCiphers[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function testChanges() {
            const letterDiv = document.getElementById('letterTest');
            const wordDiv = document.getElementById('wordTest');
            const debugDiv = document.getElementById('debugInfo');
            
            // Test letter 'a'
            const aReduction = getCipherValue('Jewish Reduction', 'a');
            const aOrdinal = getCipherValue('Jewish Ordinal', 'a');
            const aJewish = getCipherValue('Jewish', 'a');
            
            letterDiv.innerHTML = `
                <p>Letter 'a':</p>
                <p>Jewish Reduction: ${aReduction} (expected: 1)</p>
                <p>Jewish Ordinal: ${aOrdinal} (expected: 1)</p>
                <p>Jewish: ${aJewish} (expected: 1)</p>
            `;
            
            // Test word 'test'
            const testReduction = getCipherValue('Jewish Reduction', 'test');
            const testOrdinal = getCipherValue('Jewish Ordinal', 'test');
            const testJewish = getCipherValue('Jewish', 'test');
            
            // Manual calculation for 'test' with new values
            // Jewish Reduction: t=1, e=5, s=9, t=1 = 16
            // Jewish Ordinal: t=20, e=5, s=19, t=20 = 64
            // Jewish: t=100, e=5, s=90, t=100 = 295
            
            wordDiv.innerHTML = `
                <p>Word 'test':</p>
                <p>Jewish Reduction: ${testReduction} (expected: 16 = t(1)+e(5)+s(9)+t(1))</p>
                <p>Jewish Ordinal: ${testOrdinal} (expected: 64 = t(20)+e(5)+s(19)+t(20))</p>
                <p>Jewish: ${testJewish} (expected: 295 = t(100)+e(5)+s(90)+t(100))</p>
            `;
            
            // Debug info
            let debugInfo = '<h4>Cipher Debug:</h4>';
            for (let i = 0; i < allCiphers.length; i++) {
                const cipher = allCiphers[i];
                if (cipher.Nickname.includes('Jewish')) {
                    debugInfo += `<p><strong>${cipher.Nickname}:</strong></p>`;
                    debugInfo += `<p>- isJewish flag: ${cipher.isJewish}</p>`;
                    debugInfo += `<p>- Cipher type: ${cipher.cipherName}</p>`;
                    debugInfo += `<p>- Test 'a': ${cipher.Gematria('a', 1)}</p>`;
                }
            }
            
            debugDiv.innerHTML = debugInfo;
        }

        // Run test
        testChanges();
    </script>
</body>
</html>
