<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Character Mapping Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Character Mapping Analysis for Hebrew Ciphers</h1>
    
    <div class="section">
        <h3>Individual Character Values</h3>
        <table>
            <thead>
                <tr>
                    <th>Char</th>
                    <th>ASCII</th>
                    <th>Code 1 Hebrew Ordinal</th>
                    <th>Expected Ordinal</th>
                    <th>Difference</th>
                </tr>
            </thead>
            <tbody id="charTable">
            </tbody>
        </table>
    </div>

    <div class="section">
        <h3>Two-Character Combinations</h3>
        <table>
            <thead>
                <tr>
                    <th>Combination</th>
                    <th>Code 1 Value</th>
                    <th>Sum of Individual</th>
                    <th>Difference</th>
                    <th>Pattern</th>
                </tr>
            </thead>
            <tbody id="comboTable">
            </tbody>
        </table>
    </div>

    <div class="section">
        <h3>Analysis Results</h3>
        <div id="analysisResults"></div>
    </div>

    <!-- Load Code 1 -->
    <script src="Code 1/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        const code1_allCiphers = [...allCiphers];

        function getCipherValue(cipherList, cipherName, text) {
            for (let i = 0; i < cipherList.length; i++) {
                if (cipherList[i].Nickname === cipherName) {
                    return cipherList[i].Gematria(text, 1);
                }
            }
            return 'N/A';
        }

        function analyzeCharacters() {
            const charTable = document.getElementById('charTable');
            const charValues = {};
            
            // Test all printable ASCII characters
            for (let ascii = 32; ascii <= 126; ascii++) {
                const char = String.fromCharCode(ascii);
                const code1Value = getCipherValue(code1_allCiphers, 'Hebrew Ordinal', char);
                const expectedOrdinal = (ascii >= 65 && ascii <= 90) ? ascii - 64 : 
                                       (ascii >= 97 && ascii <= 122) ? ascii - 96 : 0;
                const difference = code1Value - expectedOrdinal;
                
                charValues[char] = code1Value;
                
                const row = charTable.insertRow();
                row.innerHTML = `
                    <td>${char === ' ' ? '(space)' : char}</td>
                    <td>${ascii}</td>
                    <td>${code1Value}</td>
                    <td>${expectedOrdinal}</td>
                    <td>${difference}</td>
                `;
            }
            
            return charValues;
        }

        function analyzeCombinations(charValues) {
            const comboTable = document.getElementById('comboTable');
            const testCombos = [
                'aa', 'ab', 'ac', 'ba', 'bb', 'bc', 'ca', 'cb', 'cc',
                'te', 'es', 'st', 'et', 'se', 'ts',
                'a ', ' a', 'a1', '1a', 'a!', '!a'
            ];
            
            for (const combo of testCombos) {
                const code1Value = getCipherValue(code1_allCiphers, 'Hebrew Ordinal', combo);
                const sumIndividual = combo.split('').reduce((sum, char) => {
                    return sum + (charValues[char] || 0);
                }, 0);
                const difference = code1Value - sumIndividual;
                
                let pattern = 'Additive';
                if (difference !== 0) {
                    pattern = `Non-additive (${difference > 0 ? '+' : ''}${difference})`;
                }
                
                const row = comboTable.insertRow();
                row.innerHTML = `
                    <td>${combo}</td>
                    <td>${code1Value}</td>
                    <td>${sumIndividual}</td>
                    <td>${difference}</td>
                    <td>${pattern}</td>
                `;
            }
        }

        function generateAnalysis(charValues) {
            const analysisDiv = document.getElementById('analysisResults');
            
            // Analyze patterns in character values
            const letterValues = {};
            const digitValues = {};
            const specialValues = {};
            
            for (const [char, value] of Object.entries(charValues)) {
                const ascii = char.charCodeAt(0);
                if (ascii >= 65 && ascii <= 90) { // A-Z
                    letterValues[char] = value;
                } else if (ascii >= 97 && ascii <= 122) { // a-z
                    letterValues[char] = value;
                } else if (ascii >= 48 && ascii <= 57) { // 0-9
                    digitValues[char] = value;
                } else {
                    specialValues[char] = value;
                }
            }
            
            let analysis = '<h4>Character Value Patterns:</h4>';
            
            // Check if letters follow ordinal pattern
            const ordinalPattern = Object.keys(letterValues).every(char => {
                const ascii = char.charCodeAt(0);
                const expected = (ascii >= 65 && ascii <= 90) ? ascii - 64 : ascii - 96;
                return letterValues[char] === expected;
            });
            
            analysis += `<p><strong>Letters follow ordinal pattern:</strong> ${ordinalPattern ? 'Yes' : 'No'}</p>`;
            
            // Check digit patterns
            const digitPattern = Object.keys(digitValues).every(char => {
                const expected = parseInt(char);
                return digitValues[char] === expected;
            });
            
            analysis += `<p><strong>Digits follow numeric pattern:</strong> ${digitPattern ? 'Yes' : 'No'}</p>`;
            
            // Show special character values
            analysis += '<p><strong>Special character values:</strong></p><ul>';
            for (const [char, value] of Object.entries(specialValues)) {
                const displayChar = char === ' ' ? '(space)' : char;
                analysis += `<li>${displayChar}: ${value}</li>`;
            }
            analysis += '</ul>';
            
            analysisDiv.innerHTML = analysis;
        }

        // Run the analysis
        const charValues = analyzeCharacters();
        analyzeCombinations(charValues);
        generateAnalysis(charValues);
    </script>
</body>
</html>
