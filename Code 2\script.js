document.addEventListener('DOMContentLoaded', function() {
    // Initialize the cipher system from Code 1
    Gem_Launch();

    // Get DOM elements
    const inputText = document.getElementById('input-text');
    const resultsContainer = document.getElementById('results-container');
    const summaryContainer = document.getElementById('summary-container');
    const selectAllBtn = document.getElementById('select-all-btn');
    const clearAllBtn = document.getElementById('clear-all-btn');
    const cipherFiltersContainer = document.getElementById('cipher-filters');
    const cipherSearchInput = document.getElementById('cipher-search');

    // Store selected ciphers using cipher names
    let selectedCiphers = {};

    // Populate cipher filters using the new cipher system
    function populateCipherFilters() {
        cipherFiltersContainer.innerHTML = '';

        // Group ciphers by category using the new system
        const categories = {};

        // Initialize categories from catArr
        for (let i = 0; i < catArr.length; i++) {
            categories[catArr[i]] = [];
        }

        // Group ciphers by their category
        for (let i = 0; i < allCiphers.length; i++) {
            const cipher = allCiphers[i];
            const category = cipherArray[cipher.Nickname] || "Other";

            if (!categories[category]) {
                categories[category] = [];
            }

            categories[category].push(cipher);
        }

        // Create category sections and add ciphers
        for (const category in categories) {
            if (categories[category].length === 0) continue;

            // Create category header
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'cipher-category-header';
            categoryHeader.textContent = category;
            categoryHeader.dataset.category = category;
            cipherFiltersContainer.appendChild(categoryHeader);

            // Create container for category items
            const categoryItemsContainer = document.createElement('div');
            categoryItemsContainer.className = 'cipher-category-items';
            categoryItemsContainer.dataset.category = category;
            cipherFiltersContainer.appendChild(categoryItemsContainer);

            // Add click event to toggle category items
            categoryHeader.addEventListener('click', function(e) {
                // Prevent checkbox clicks from triggering this
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'LABEL') {
                    return;
                }

                const categoryItems = document.querySelector(`.cipher-category-items[data-category="${category}"]`);
                if (categoryItems.style.display === 'none') {
                    categoryItems.style.display = 'block';
                    this.classList.remove('collapsed');
                } else {
                    categoryItems.style.display = 'none';
                    this.classList.add('collapsed');
                }
            });

            // Add ciphers for this category
            categories[category].forEach(cipher => {
                const cipherName = cipher.Nickname;

                // Create filter item
                const filterItem = document.createElement('div');
                filterItem.className = 'cipher-filter-item';

                // Create checkbox
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = cipherName.replace(/\s+/g, '-').toLowerCase();
                checkbox.checked = openCiphers.includes(cipherName); // Use openCiphers from Code 1

                // Create label
                const label = document.createElement('label');
                label.htmlFor = checkbox.id;
                label.textContent = cipherName;

                // Add to filter item
                filterItem.appendChild(checkbox);
                filterItem.appendChild(label);

                // Add to category container
                categoryItemsContainer.appendChild(filterItem);

                // Store initial state
                selectedCiphers[cipherName] = checkbox.checked;

                // Add event listener
                checkbox.addEventListener('change', function() {
                    selectedCiphers[cipherName] = this.checked;
                    calculateGematria();
                });
            });
        }
    }

    // Initialize by selecting all ciphers
    function initializeFilters() {
        populateCipherFilters();

        // Add event listeners to buttons
        selectAllBtn.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.cipher-filter-item input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                // Find cipher name from label
                const label = checkbox.nextElementSibling;
                const cipherName = label.textContent;
                selectedCiphers[cipherName] = true;
            });
            calculateGematria();
        });

        clearAllBtn.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.cipher-filter-item input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                // Find cipher name from label
                const label = checkbox.nextElementSibling;
                const cipherName = label.textContent;
                selectedCiphers[cipherName] = false;
            });
            calculateGematria();
        });

        // Add select all and clear all buttons for each category
        document.querySelectorAll('.cipher-category-header').forEach(header => {
            const categoryName = header.dataset.category;

            // Add right-click functionality to toggle all ciphers in that category
            header.addEventListener('contextmenu', function(e) {
                e.preventDefault(); // Prevent the default context menu

                const categoryContainer = document.querySelector(`.cipher-category-items[data-category="${categoryName}"]`);

                if (!categoryContainer) return;

                const categoryItems = categoryContainer.querySelectorAll('.cipher-filter-item');

                // Check if all items are checked
                const allChecked = Array.from(categoryItems).every(item =>
                    item.querySelector('input[type="checkbox"]').checked
                );

                // Toggle all items
                categoryItems.forEach(item => {
                    const checkbox = item.querySelector('input[type="checkbox"]');
                    const label = checkbox.nextElementSibling;
                    const cipherName = label.textContent;
                    checkbox.checked = !allChecked;
                    selectedCiphers[cipherName] = !allChecked;
                });

                calculateGematria();
            });
        });

        // Add search functionality
        cipherSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const filterItems = document.querySelectorAll('.cipher-filter-item');
            const categoryHeaders = document.querySelectorAll('.cipher-category-header');
            const categoryContainers = document.querySelectorAll('.cipher-category-items');

            // Create a map to track which categories have visible items
            const categoriesWithVisibleItems = {};

            // Initialize all categories as having no visible items
            categoryHeaders.forEach(header => {
                const category = header.dataset.category;
                categoriesWithVisibleItems[category] = false;
            });

            // Check each filter item
            filterItems.forEach(item => {
                const label = item.querySelector('label');
                const cipherName = label.textContent.toLowerCase();

                // Get the parent category container
                const categoryContainer = item.closest('.cipher-category-items');
                const category = categoryContainer ? categoryContainer.dataset.category : null;

                if (cipherName.includes(searchTerm) || (category && category.toLowerCase().includes(searchTerm))) {
                    item.style.display = 'flex';
                    if (category) {
                        categoriesWithVisibleItems[category] = true;
                    }
                } else {
                    item.style.display = 'none';
                }
            });

            // Show/hide category headers and containers based on whether they have visible items
            categoryHeaders.forEach(header => {
                const category = header.dataset.category;
                if (categoriesWithVisibleItems[category]) {
                    header.style.display = 'block';
                    // Find and show the corresponding container
                    const container = document.querySelector(`.cipher-category-items[data-category="${category}"]`);
                    if (container) {
                        container.style.display = 'block';
                        // Make sure the header is not collapsed
                        header.classList.remove('collapsed');
                    }
                } else {
                    header.style.display = 'none';
                    // Find and hide the corresponding container
                    const container = document.querySelector(`.cipher-category-items[data-category="${category}"]`);
                    if (container) {
                        container.style.display = 'none';
                    }
                }
            });

            // If search is empty, restore all categories
            if (searchTerm === '') {
                categoryHeaders.forEach(header => {
                    header.style.display = 'block';
                });
                categoryContainers.forEach(container => {
                    container.style.display = 'block';
                });
            }
        });
    }

    // Set up auto-calculation with debounce
    let debounceTimer;
    inputText.addEventListener('input', function() {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(function() {
            calculateGematria();
        }, 300); // 300ms debounce delay
    });

    // Add event listener for Enter key in textarea
    inputText.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            calculateGematria();
        }
    });

    // Main function to calculate gematria values using Code 1 system
    function calculateGematria() {
        const text = inputText.value.trim();

        if (!text) {
            resultsContainer.innerHTML = '<p class="empty-result">Please enter some text to calculate</p>';
            summaryContainer.innerHTML = '<p class="empty-result">Enter text to see summary</p>';
            return;
        }

        // Clear previous results
        resultsContainer.innerHTML = '';
        summaryContainer.innerHTML = '';

        // Store results grouped by category
        const resultsByCategory = {};
        const summaryResults = [];

        // Process each cipher using the new system
        for (let i = 0; i < allCiphers.length; i++) {
            const cipher = allCiphers[i];
            const cipherName = cipher.Nickname;

            // Skip if cipher is not selected
            if (!selectedCiphers[cipherName]) continue;

            // Calculate using Code 1's Gematria method
            const totalValue = cipher.Gematria(text, 1);
            const category = cipherArray[cipherName] || "Other";

            // Group results by category
            if (!resultsByCategory[category]) {
                resultsByCategory[category] = [];
            }

            resultsByCategory[category].push({
                cipher: cipher,
                cipherName: cipherName,
                totalValue: totalValue,
                category: category
            });

            // Store result for summary
            summaryResults.push({
                name: cipherName,
                totalValue: totalValue,
                category: category,
                rgb: cipher.RGB
            });
        }

        // Display results grouped by category
        displayGroupedResults(resultsByCategory, text);

        // Create summary elements in the same order as main results
        createSummaryElements(summaryResults);

        // Show message if no ciphers are selected
        if (resultsContainer.children.length === 0) {
            resultsContainer.innerHTML = '<p class="empty-result">No ciphers selected. Please select at least one cipher from the sidebar.</p>';
            summaryContainer.innerHTML = '<p class="empty-result">No ciphers selected. Please select at least one cipher from the sidebar.</p>';
        }
    }

    // Display results grouped by category
    function displayGroupedResults(resultsByCategory, text) {
        // Define category order
        const categoryOrder = ["English", "Reverse", "Jewish", "Kabbalah", "Mathematical", "Other"];

        for (const category of categoryOrder) {
            if (!resultsByCategory[category] || resultsByCategory[category].length === 0) continue;

            // Create category header
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'result-category-header';
            categoryHeader.textContent = category;
            resultsContainer.appendChild(categoryHeader);

            // Create category container
            const categoryContainer = document.createElement('div');
            categoryContainer.className = 'result-category-container';

            // Add each cipher result in this category
            for (const result of resultsByCategory[category]) {
                const resultElement = createResultElement(result.cipher, result.totalValue, text);
                categoryContainer.appendChild(resultElement);
            }

            resultsContainer.appendChild(categoryContainer);
        }
    }

    // Helper function to get cipher mapping (letter to value)
    function getCipherMapping(cipher) {
        const mapping = {};
        const alphabet = 'abcdefghijklmnopqrstuvwxyz';

        for (let i = 0; i < alphabet.length; i++) {
            const letter = alphabet[i];
            const value = cipher.Gematria(letter, 1);
            if (value > 0) {
                mapping[letter] = value;
            }
        }

        return mapping;
    }

    // Helper function to get cipher rule description
    function getCipherRuleDescription(cipher, mapping) {
        const cipherName = cipher.Nickname;

        // Special descriptions for known ciphers
        if (cipherName === "English Ordinal") {
            return "A = 1, B = 2, C = 3, ..., Z = 26";
        } else if (cipherName === "Full Reduction") {
            return "A = 1, B = 2, ..., I = 9, J = 1, K = 2, ..., R = 9, S = 1, T = 2, ..., Z = 8";
        } else if (cipherName === "Single Reduction") {
            return "A = 1, B = 2, ..., I = 9, J = 1, K = 2, ..., R = 9, S = 10, T = 2, ..., Z = 8";
        } else if (cipherName === "Single Reduction KV") {
            return "Like Single Reduction but K = 11, V = 22 (master numbers preserved)";
        } else if (cipherName === "Extended English") {
            return "A = 1, B = 2, ..., I = 9, J = 10, K = 20, L = 30, ..., Z = 800";
        } else if (cipherName === "Alphanumeric Qabbala") {
            return "Base-36 notation: A = 10, B = 11, C = 12, ..., Z = 35";
        } else if (cipherName === "Francis Bacon") {
            return "A = 1, B = 2, C = 3, ..., Z = 26 (case-sensitive: capitals +26)";
        } else if (cipherName === "Satanic") {
            return "A = 36, B = 37, C = 38, ..., Z = 61";
        } else if (cipherName.includes("Reverse")) {
            if (cipherName === "Reverse Ordinal") {
                return "Z = 1, Y = 2, X = 3, ..., A = 26";
            } else if (cipherName === "Reverse Single Reduction") {
                return "Z = 1, Y = 2, ..., R = 9, Q = 1, P = 2, ..., I = 9, H = 10, G = 2, ..., A = 8";
            } else if (cipherName === "Reverse Extended") {
                return "Z = 1, Y = 2, ..., R = 9, Q = 10, P = 20, O = 30, ..., A = 800";
            } else {
                return "Reverse alphabet order with cipher-specific values";
            }
        } else if (cipherName.includes("Jewish")) {
            if (cipherName === "Jewish") {
                return "A = 1, B = 2, ..., I = 9, J = 10, K = 20, L = 30, ..., S = 100, T = 200, ..., Z = 800";
            } else if (cipherName === "Jewish Reduction") {
                return "Jewish values reduced to single digits (1-9)";
            } else if (cipherName === "Jewish Ordinal") {
                return "Non-standard ordering: A = 1, B = 2, ..., I = 9, J = 24, K = 11, ..., V = 25, W = 27";
            } else {
                return "Jewish gematria values with traditional Hebrew letter mappings";
            }
        } else if (cipherName.includes("Kabbalah")) {
            return `Kabbalistic cipher: ${cipherName.replace(" Kabbalah", "")} variant`;
        } else if (cipherName.includes("Primes")) {
            return "Values based on prime numbers: A = 2, B = 3, C = 5, D = 7, E = 11, ...";
        } else if (cipherName.includes("Squares")) {
            return "Values based on perfect squares: A = 1, B = 4, C = 9, D = 16, E = 25, ...";
        } else if (cipherName.includes("Trigonal")) {
            return "Values based on triangular numbers: A = 1, B = 3, C = 6, D = 10, E = 15, ...";
        } else if (cipherName === "Chaldean") {
            return "Ancient Chaldean numerology: A = 1, B = 2, C = 3, D = 4, E = 5, F = 8, ...";
        } else if (cipherName === "Septenary") {
            return "Seven-based system with mirrored values: A = 1, B = 2, ..., G = 7, H = 6, ..., N = 1";
        } else {
            // Generate a compact description from the mapping
            const values = Object.values(mapping);
            const minVal = Math.min(...values);
            const maxVal = Math.max(...values);
            return `Values range from ${minVal} to ${maxVal}`;
        }
    }

    // Helper function to calculate word breakdown
    function getWordBreakdown(text, cipher, mapping) {
        const words = text.trim().split(/\s+/);
        const breakdown = [];

        for (const word of words) {
            if (!word) continue;

            const letters = [];
            let wordTotal = 0;

            for (const char of word) {
                const lowerChar = char.toLowerCase();
                if (mapping[lowerChar] !== undefined) {
                    const value = mapping[lowerChar];
                    letters.push(`${char}=${value}`);
                    wordTotal += value;
                } else if (lowerChar >= '0' && lowerChar <= '9') {
                    const value = parseInt(lowerChar);
                    letters.push(`${char}=${value}`);
                    wordTotal += value;
                }
            }

            if (letters.length > 0) {
                breakdown.push({
                    word: word,
                    letters: letters,
                    total: wordTotal
                });
            }
        }

        return breakdown;
    }

    // Create result element for a cipher with detailed breakdown
    function createResultElement(cipher, totalValue, text) {
        const div = document.createElement('div');
        div.className = 'cipher-result-new';

        // Get cipher mapping and breakdown
        const mapping = getCipherMapping(cipher);
        const wordBreakdown = getWordBreakdown(text, cipher, mapping);

        // Clean cipher name
        let cleanName = cipher.Nickname;
        const categories = ["English", "Reverse", "Jewish", "Kabbalah", "Mathematical", "Other"];
        for (const cat of categories) {
            if (cleanName.endsWith(cat)) {
                cleanName = cleanName.substring(0, cleanName.length - cat.length).trim();
                break;
            }
        }

        // Get category for display
        const category = cipherArray[cipher.Nickname] || "Other";

        // Create letter grid for the cipher (showing all 26 letters)
        const alphabet = 'abcdefghijklmnopqrstuvwxyz';
        const letterGrid = alphabet.split('').map(letter => {
            const value = mapping[letter] || 0;
            return `<div class="letter-cell">
                <div class="letter">${letter.toUpperCase()}</div>
                <div class="letter-value">${value}</div>
            </div>`;
        }).join('');

        // Create word breakdown display with letter grid format
        const wordBreakdownHtml = wordBreakdown.map(item => {
            // Create individual letter cells for this word
            const letterCells = item.word.split('').map(char => {
                const lowerChar = char.toLowerCase();
                const value = mapping[lowerChar] || (char >= '0' && char <= '9' ? parseInt(char) : 0);
                return `<div class="word-letter-cell">
                    <div class="word-letter">${char.toUpperCase()}</div>
                    <div class="word-letter-value">${value}</div>
                </div>`;
            }).join('');

            return `
                <div class="word-breakdown-item">
                    <div class="word-header">
                        <span class="word-name">"${item.word}"</span>
                        <span class="word-total">${item.total}</span>
                    </div>
                    <div class="word-letter-grid">
                        ${letterCells}
                    </div>
                </div>
            `;
        }).join('');

        // Create the result HTML
        div.innerHTML = `
            <div class="cipher-header-new">
                <div class="cipher-title">
                    <span class="cipher-name">${cleanName}</span>
                    <span class="cipher-category-tag">${category}</span>
                </div>
                <span class="cipher-total">${totalValue}</span>
            </div>

            <div class="word-breakdown-section">
                <div class="word-breakdown-title">Word Breakdown:</div>
                ${wordBreakdownHtml}
            </div>

            <div class="cipher-alphabet-section">
                <div class="cipher-alphabet-title">${cleanName}</div>
                <div class="letter-grid">
                    ${letterGrid}
                </div>
            </div>
        `;

        return div;
    }
    // Create summary elements using Code 1 system
    function createSummaryElements(summaryResults) {
        // Group summary results by category
        const categorizedResults = {};

        // Initialize categories from catArr
        for (let i = 0; i < catArr.length; i++) {
            categorizedResults[catArr[i]] = [];
        }

        // Group results by category
        summaryResults.forEach(result => {
            const category = result.category;

            if (!categorizedResults[category]) {
                categorizedResults[category] = [];
            }

            categorizedResults[category].push(result);
        });

        // Create summary items grouped by category
        for (const category in categorizedResults) {
            if (categorizedResults[category].length === 0) continue;

            // Create category header
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'summary-category-header';
            categoryHeader.textContent = category;
            categoryHeader.dataset.category = category;
            summaryContainer.appendChild(categoryHeader);

            // Create container for this category's items
            const categoryItemsContainer = document.createElement('div');
            categoryItemsContainer.className = 'summary-category-items';
            categoryItemsContainer.dataset.category = category;
            summaryContainer.appendChild(categoryItemsContainer);

            // Add items for this category
            categorizedResults[category].forEach(result => {
                const summaryItem = document.createElement('div');
                summaryItem.className = 'summary-item';
                summaryItem.dataset.category = category;

                const nameSpan = document.createElement('span');
                nameSpan.className = 'summary-name';
                nameSpan.textContent = result.name;

                const valuesSpan = document.createElement('span');
                valuesSpan.className = 'summary-values';

                // Only show the total value
                valuesSpan.textContent = result.totalValue.toString();

                summaryItem.appendChild(nameSpan);
                summaryItem.appendChild(valuesSpan);

                categoryItemsContainer.appendChild(summaryItem);
            });

            // Add click event to toggle category items
            categoryHeader.addEventListener('click', function() {
                const categoryItems = document.querySelector(`.summary-category-items[data-category="${category}"]`);
                if (categoryItems.style.display === 'none') {
                    categoryItems.style.display = 'block';
                    this.classList.remove('collapsed');
                } else {
                    categoryItems.style.display = 'none';
                    this.classList.add('collapsed');
                }
            });
        }
    }

    // Initialize filters
    initializeFilters();

    // Initial calculation if there's text in the input
    calculateGematria();
});